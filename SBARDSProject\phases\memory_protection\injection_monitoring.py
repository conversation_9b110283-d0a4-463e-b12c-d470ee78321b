"""
Comprehensive Memory Injection Monitoring System (6.1.1) - SBARDS Memory Protection Layer
Implements continuous monitoring of memory allocation/modification, code injection detection,
behavioral analysis, and ROP attack protection.
"""

import os
import sys
import time
import logging
import psutil
import threading
import ctypes
from typing import Dict, List, Any, Optional, Callable, Set, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque
import platform

@dataclass
class MemoryAllocation:
    """Memory allocation event"""
    process_id: int
    address: int
    size: int
    protection: str
    allocation_type: str
    timestamp: datetime
    call_stack: Optional[List[str]] = None

@dataclass
class MemoryModification:
    """Memory modification event"""
    process_id: int
    address: int
    size: int
    old_protection: str
    new_protection: str
    modification_type: str
    timestamp: datetime
    suspicious_score: float = 0.0

@dataclass
class InjectionDetection:
    """Code injection detection result"""
    process_id: int
    injection_type: str
    confidence: float
    evidence: List[str]
    memory_regions: List[Tuple[int, int]]  # (start, end) addresses
    timestamp: datetime
    risk_level: str

class InjectionTechnique(Enum):
    """Known injection techniques"""
    DLL_INJECTION = "dll_injection"
    PROCESS_HOLLOWING = "process_hollowing"
    REFLECTIVE_DLL = "reflective_dll"
    THREAD_HIJACKING = "thread_hijacking"
    ATOM_BOMBING = "atom_bombing"
    MANUAL_DLL_LOADING = "manual_dll_loading"
    PROCESS_DOPPELGANGING = "process_doppelganging"
    PROCESS_GHOSTING = "process_ghosting"

class ROPGadget:
    """ROP gadget detection"""
    def __init__(self, address: int, instructions: bytes, gadget_type: str):
        self.address = address
        self.instructions = instructions
        self.gadget_type = gadget_type
        self.usage_count = 0
        self.last_used = datetime.now()

class MemoryInjectionMonitor:
    """
    Comprehensive Memory Injection Monitoring System (6.1.1)
    Continuous monitoring of memory allocation and modification operations
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("SBARDS.InjectionMonitor")
        
        # Monitoring configuration
        self.monitoring_enabled = config.get("monitoring_enabled", True)
        self.scan_interval = config.get("scan_interval_seconds", 5)
        self.max_events_per_process = config.get("max_events_per_process", 1000)
        self.suspicious_threshold = config.get("suspicious_threshold", 0.7)
        
        # Event storage
        self.memory_allocations: Dict[int, deque] = defaultdict(lambda: deque(maxlen=self.max_events_per_process))
        self.memory_modifications: Dict[int, deque] = defaultdict(lambda: deque(maxlen=self.max_events_per_process))
        self.injection_detections: List[InjectionDetection] = []
        
        # Process monitoring
        self.monitored_processes: Set[int] = set()
        self.process_baselines: Dict[int, Dict[str, Any]] = {}
        
        # ROP protection
        self.rop_gadgets: Dict[int, List[ROPGadget]] = defaultdict(list)
        self.rop_chains_detected: List[Dict[str, Any]] = []
        
        # Behavioral analysis
        self.behavior_patterns: Dict[int, Dict[str, Any]] = defaultdict(dict)
        
        # Threading
        self._monitor_thread = None
        self._stop_monitoring = threading.Event()
        self._lock = threading.RLock()
        
        # Injection detection patterns
        self.injection_patterns = self._load_injection_patterns()
        
        self.logger.info("Memory Injection Monitor initialized")
    
    def _load_injection_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load patterns for detecting various injection techniques"""
        return {
            InjectionTechnique.DLL_INJECTION.value: {
                "apis": ["LoadLibraryA", "LoadLibraryW", "GetProcAddress"],
                "memory_patterns": [b"LoadLibrary", b"GetProcAddress"],
                "suspicious_allocations": ["PAGE_EXECUTE_READWRITE"],
                "confidence_weight": 0.8
            },
            InjectionTechnique.PROCESS_HOLLOWING.value: {
                "apis": ["NtUnmapViewOfSection", "NtWriteVirtualMemory", "ResumeThread"],
                "memory_patterns": [b"NtUnmapViewOfSection", b"NtWriteVirtualMemory"],
                "suspicious_allocations": ["PAGE_EXECUTE_READWRITE", "PAGE_READWRITE"],
                "confidence_weight": 0.9
            },
            InjectionTechnique.REFLECTIVE_DLL.value: {
                "apis": ["VirtualAlloc", "VirtualProtect", "GetProcAddress"],
                "memory_patterns": [b"VirtualAlloc", b"VirtualProtect"],
                "suspicious_allocations": ["PAGE_EXECUTE_READWRITE"],
                "confidence_weight": 0.7
            },
            InjectionTechnique.THREAD_HIJACKING.value: {
                "apis": ["SuspendThread", "GetThreadContext", "SetThreadContext", "ResumeThread"],
                "memory_patterns": [b"SuspendThread", b"SetThreadContext"],
                "suspicious_allocations": ["PAGE_EXECUTE"],
                "confidence_weight": 0.8
            }
        }
    
    def start_monitoring(self) -> bool:
        """Start continuous memory injection monitoring"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            return True
        
        try:
            self._stop_monitoring.clear()
            self._monitor_thread = threading.Thread(target=self._monitoring_worker, daemon=True)
            self._monitor_thread.start()
            
            self.logger.info("Memory injection monitoring started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start monitoring: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """Stop memory injection monitoring"""
        try:
            self._stop_monitoring.set()
            if self._monitor_thread:
                self._monitor_thread.join(timeout=10)
            
            self.logger.info("Memory injection monitoring stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop monitoring: {e}")
            return False
    
    def _monitoring_worker(self):
        """Worker thread for continuous monitoring"""
        while not self._stop_monitoring.wait(self.scan_interval):
            try:
                self._scan_all_processes()
                self._analyze_behavioral_patterns()
                self._detect_rop_chains()
                self._cleanup_old_events()
                
            except Exception as e:
                self.logger.error(f"Monitoring worker error: {e}")
    
    def _scan_all_processes(self):
        """Scan all processes for injection indicators"""
        try:
            current_processes = set(p.pid for p in psutil.process_iter(['pid']))
            
            # Check for new processes
            new_processes = current_processes - self.monitored_processes
            for pid in new_processes:
                self._establish_process_baseline(pid)
            
            # Update monitored processes
            self.monitored_processes = current_processes
            
            # Scan each process
            for pid in current_processes:
                self._scan_process_memory(pid)
                
        except Exception as e:
            self.logger.error(f"Process scanning error: {e}")
    
    def _establish_process_baseline(self, pid: int):
        """Establish baseline for new process"""
        try:
            process = psutil.Process(pid)
            
            # Get initial memory maps
            try:
                memory_maps = process.memory_maps()
                baseline = {
                    "name": process.name(),
                    "exe": process.exe() if process.exe() else "unknown",
                    "create_time": process.create_time(),
                    "initial_memory_regions": len(memory_maps),
                    "initial_executable_regions": sum(1 for m in memory_maps if 'x' in getattr(m, 'perms', '')),
                    "baseline_established": datetime.now()
                }
                
                self.process_baselines[pid] = baseline
                self.logger.debug(f"Established baseline for process {pid} ({baseline['name']})")
                
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
                
        except Exception as e:
            self.logger.debug(f"Failed to establish baseline for PID {pid}: {e}")
    
    def _scan_process_memory(self, pid: int):
        """Scan individual process for injection indicators"""
        try:
            process = psutil.Process(pid)
            
            # Get current memory maps
            try:
                memory_maps = process.memory_maps()
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                return
            
            # Analyze memory regions for suspicious characteristics
            suspicious_regions = []
            for mmap_info in memory_maps:
                if self._is_region_suspicious(mmap_info, pid):
                    suspicious_regions.append(mmap_info)
            
            # Check for injection patterns
            if suspicious_regions:
                self._analyze_injection_patterns(pid, suspicious_regions)
            
            # Monitor memory allocations (simulated)
            self._simulate_allocation_monitoring(pid, memory_maps)
            
        except Exception as e:
            self.logger.debug(f"Process memory scan error for PID {pid}: {e}")
    
    def _is_region_suspicious(self, mmap_info: Any, pid: int) -> bool:
        """Determine if memory region is suspicious"""
        try:
            perms = getattr(mmap_info, 'perms', '')
            path = getattr(mmap_info, 'path', '')
            
            # Writable and executable regions are suspicious
            if 'w' in perms and 'x' in perms:
                return True
            
            # Anonymous executable regions
            if 'x' in perms and (path == '' or '[heap]' in path):
                return True
            
            # Check against baseline
            if pid in self.process_baselines:
                baseline = self.process_baselines[pid]
                current_time = datetime.now()
                
                # New executable regions after process start
                if 'x' in perms and (current_time - baseline["baseline_established"]).seconds > 60:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _analyze_injection_patterns(self, pid: int, suspicious_regions: List[Any]):
        """Analyze suspicious regions for injection patterns"""
        try:
            detections = []
            
            for technique, pattern_info in self.injection_patterns.items():
                confidence = 0.0
                evidence = []
                
                # Check for API patterns (simulated)
                api_matches = 0
                for api in pattern_info["apis"]:
                    # In production, would scan actual memory for API references
                    if self._simulate_api_detection(pid, api):
                        api_matches += 1
                        evidence.append(f"API detected: {api}")
                
                if api_matches > 0:
                    confidence += (api_matches / len(pattern_info["apis"])) * pattern_info["confidence_weight"]
                
                # Check memory allocation patterns
                for region in suspicious_regions:
                    perms = getattr(region, 'perms', '')
                    if any(susp_alloc in perms for susp_alloc in pattern_info["suspicious_allocations"]):
                        confidence += 0.2
                        evidence.append(f"Suspicious memory allocation: {perms}")
                
                # Create detection if confidence is high enough
                if confidence >= self.suspicious_threshold:
                    detection = InjectionDetection(
                        process_id=pid,
                        injection_type=technique,
                        confidence=min(confidence, 1.0),
                        evidence=evidence,
                        memory_regions=[(0, 0)],  # Simplified for simulation
                        timestamp=datetime.now(),
                        risk_level="high" if confidence > 0.8 else "medium"
                    )
                    
                    detections.append(detection)
                    self.logger.warning(f"Injection detected: {technique} in PID {pid} (confidence: {confidence:.2f})")
            
            # Store detections
            with self._lock:
                self.injection_detections.extend(detections)
                
        except Exception as e:
            self.logger.error(f"Injection pattern analysis error: {e}")
    
    def _simulate_api_detection(self, pid: int, api_name: str) -> bool:
        """Simulate API detection in process memory"""
        # In production, this would scan actual process memory for API references
        # For simulation, we'll randomly detect some APIs based on process characteristics
        try:
            process = psutil.Process(pid)
            process_name = process.name().lower()
            
            # Simulate higher detection rates for certain process types
            suspicious_processes = ['powershell', 'cmd', 'rundll32', 'regsvr32']
            if any(susp in process_name for susp in suspicious_processes):
                return True  # Simulate detection
            
            return False  # No detection
            
        except:
            return False
    
    def _simulate_allocation_monitoring(self, pid: int, memory_maps: List[Any]):
        """Simulate memory allocation monitoring"""
        # In production, this would hook into actual memory allocation APIs
        current_time = datetime.now()
        
        # Simulate allocation events for processes with suspicious characteristics
        try:
            process = psutil.Process(pid)
            
            # Check if process has grown significantly
            if pid in self.process_baselines:
                baseline = self.process_baselines[pid]
                current_regions = len(memory_maps)
                initial_regions = baseline["initial_memory_regions"]
                
                if current_regions > initial_regions * 1.5:  # 50% growth
                    # Simulate allocation event
                    allocation = MemoryAllocation(
                        process_id=pid,
                        address=0x10000000,  # Simulated address
                        size=4096,
                        protection="PAGE_EXECUTE_READWRITE",
                        allocation_type="VirtualAlloc",
                        timestamp=current_time
                    )
                    
                    with self._lock:
                        self.memory_allocations[pid].append(allocation)
                    
                    self.logger.debug(f"Simulated suspicious allocation in PID {pid}")
                    
        except Exception:
            pass
    
    def _analyze_behavioral_patterns(self):
        """Analyze behavioral patterns for injection detection"""
        with self._lock:
            for pid in self.monitored_processes:
                try:
                    # Analyze allocation patterns
                    allocations = list(self.memory_allocations[pid])
                    if len(allocations) > 5:  # Minimum events for analysis
                        
                        # Check for rapid allocations
                        recent_allocations = [a for a in allocations 
                                            if (datetime.now() - a.timestamp).seconds < 60]
                        
                        if len(recent_allocations) > 10:  # Many allocations in short time
                            self.behavior_patterns[pid]["rapid_allocation"] = {
                                "count": len(recent_allocations),
                                "detected_at": datetime.now(),
                                "risk_score": min(len(recent_allocations) / 20.0, 1.0)
                            }
                    
                    # Analyze modification patterns
                    modifications = list(self.memory_modifications[pid])
                    if modifications:
                        # Check for protection changes
                        protection_changes = [m for m in modifications 
                                            if "EXECUTE" in m.new_protection and "EXECUTE" not in m.old_protection]
                        
                        if protection_changes:
                            self.behavior_patterns[pid]["protection_changes"] = {
                                "count": len(protection_changes),
                                "detected_at": datetime.now(),
                                "risk_score": min(len(protection_changes) / 5.0, 1.0)
                            }
                
                except Exception as e:
                    self.logger.debug(f"Behavioral analysis error for PID {pid}: {e}")
    
    def _detect_rop_chains(self):
        """Detect ROP (Return-Oriented Programming) chains"""
        # ROP detection is complex and would require deep memory analysis
        # This is a simplified simulation
        
        for pid in self.monitored_processes:
            try:
                # Simulate ROP gadget detection
                if pid in self.behavior_patterns:
                    patterns = self.behavior_patterns[pid]
                    
                    # If we have suspicious patterns, simulate ROP detection
                    if ("rapid_allocation" in patterns and patterns["rapid_allocation"]["risk_score"] > 0.7) or \
                       ("protection_changes" in patterns and patterns["protection_changes"]["risk_score"] > 0.5):
                        
                        # Simulate ROP chain detection
                        rop_detection = {
                            "process_id": pid,
                            "gadget_count": 15,  # Simulated
                            "chain_length": 8,   # Simulated
                            "confidence": 0.8,
                            "detected_at": datetime.now(),
                            "gadget_addresses": [0x401000 + i*4 for i in range(8)]  # Simulated
                        }
                        
                        with self._lock:
                            self.rop_chains_detected.append(rop_detection)
                        
                        self.logger.warning(f"ROP chain detected in PID {pid}")
                        
            except Exception as e:
                self.logger.debug(f"ROP detection error for PID {pid}: {e}")
    
    def _cleanup_old_events(self):
        """Clean up old monitoring events"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        with self._lock:
            # Clean up old injection detections
            self.injection_detections = [d for d in self.injection_detections 
                                       if d.timestamp > cutoff_time]
            
            # Clean up old ROP detections
            self.rop_chains_detected = [r for r in self.rop_chains_detected 
                                      if r["detected_at"] > cutoff_time]
            
            # Clean up old behavior patterns
            for pid in list(self.behavior_patterns.keys()):
                patterns = self.behavior_patterns[pid]
                for pattern_name in list(patterns.keys()):
                    if patterns[pattern_name].get("detected_at", datetime.min) < cutoff_time:
                        del patterns[pattern_name]
                
                if not patterns:
                    del self.behavior_patterns[pid]
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status and statistics"""
        with self._lock:
            return {
                "monitoring_active": self._monitor_thread and self._monitor_thread.is_alive(),
                "monitored_processes": len(self.monitored_processes),
                "total_allocations": sum(len(allocs) for allocs in self.memory_allocations.values()),
                "total_modifications": sum(len(mods) for mods in self.memory_modifications.values()),
                "injection_detections": len(self.injection_detections),
                "rop_chains_detected": len(self.rop_chains_detected),
                "processes_with_patterns": len(self.behavior_patterns),
                "last_scan": datetime.now().isoformat()
            }
