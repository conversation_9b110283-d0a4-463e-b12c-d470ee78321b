"""
@file encrypt_endpoint.py
@brief نقطة نهاية FastAPI لتشفير الذاكرة في نظام SBARDS

هذا الملف يحتوي على واجهة برمجة التطبيقات (API) لتشفير أقسام الذاكرة
كما ورد في قسم 6.1.1 من وثيقة النظام
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import APIKeyHeader
from typing import Optional
import logging
try:
    from ..memory_protection import MemoryProtectionLayer
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from memory_protection import MemoryProtectionLayer

try:
    from ...config import load_config
except ImportError:
    # Fallback configuration loader
    def load_config():
        return {
            "memory_protection": {
                "enabled": True,
                "safe_mode": True,
                "simulation_only": True,
                "scan_interval_seconds": 120,
                "memory_threshold_mb": 200,
                "suspicious_patterns": []
            },
            "mongodb": {"enabled": False}
        }

# تهيئة المسار (Router) لنقاط النهاية
router = APIRouter()

# مصادقة مفتاح API
api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)

async def get_api_key(api_key: Optional[str] = Depends(api_key_header)):
    if api_key != "SBARDS_SECURE_API_KEY":  # في الواقع يجب أن يكون من إعدادات النظام
        raise HTTPException(
            status_code=403,
            detail="Invalid API Key"
        )
    return api_key

@router.post("/memory/encrypt",
             summary="تشفير قسم الذاكرة",
             description="يقوم بتشفير قسم الذاكرة المحدد باستخدام خوارزميات AES-256 المتقدمة")
async def encrypt_memory(api_key: str = Depends(get_api_key)):
    """
    نقطة نهاية لتشفير قسم الذاكرة

    Returns:
        dict: نتيجة عملية التشفير
    """
    try:
        # تحميل الإعدادات وتهيئة طبقة حماية الذاكرة
        config = load_config()
        memory_protection = MemoryProtectionLayer(config)

        # استدعاء دالة التشفير
        result = memory_protection.encrypt_memory_partition()

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "Encryption failed")
            )

        return {
            "status": "success",
            "message": "Memory partition encrypted successfully",
            "timestamp": result.get("timestamp")
        }

    except Exception as e:
        logging.error(f"Encryption endpoint error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Memory encryption failed"
        )