@echo off
REM Build script for SBARDS Memory Protection C++ libraries on Windows
REM This script safely builds the memory protection libraries in simulation mode

echo ========================================
echo SBARDS Memory Protection Build Script
echo ========================================
echo.
echo Building C++ libraries for Windows...
echo WARNING: This builds SIMULATION-ONLY libraries
echo No actual system memory operations will be performed
echo.

REM Check if we're in the right directory
if not exist "memory_encryptor.cpp" (
    echo ERROR: memory_encryptor.cpp not found
    echo Please run this script from the cpp directory
    pause
    exit /b 1
)

REM Create output directory
if not exist "build" mkdir build

REM Try to find Visual Studio compiler
set "VS_FOUND=0"
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    echo Found Visual Studio 2019 Build Tools
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo Found Visual Studio 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo Found Visual Studio 2022 Professional
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=1"
)

if "%VS_FOUND%"=="1" (
    echo.
    echo Building with Microsoft Visual C++ Compiler...
    echo.
    
    REM Build memory encryptor (simulation mode)
    echo Building memory_encryptor.cpp...
    cl /EHsc /DSIMULATION_MODE /Fo:build\ /Fe:build\libmemprotect.dll /LD memory_encryptor.cpp cold_boot_protector.cpp
    
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Failed to build with MSVC
        goto TryGCC
    ) else (
        echo SUCCESS: Built libmemprotect.dll with MSVC
        goto BuildComplete
    )
) else (
    echo Visual Studio not found, trying GCC...
    goto TryGCC
)

:TryGCC
echo.
echo Trying to build with GCC/MinGW...
echo.

REM Check if GCC is available
where gcc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Neither Visual Studio nor GCC found
    echo Please install one of the following:
    echo - Visual Studio Build Tools
    echo - MinGW-w64
    echo - MSYS2 with GCC
    goto BuildFailed
)

echo Building with GCC...
gcc -shared -fPIC -DSIMULATION_MODE -o build/libmemprotect.dll memory_encryptor.cpp cold_boot_protector.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to build with GCC
    goto BuildFailed
) else (
    echo SUCCESS: Built libmemprotect.dll with GCC
    goto BuildComplete
)

:BuildComplete
echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY
echo ========================================
echo.
echo Output: build\libmemprotect.dll
echo Mode: SIMULATION ONLY (Safe for testing)
echo.
echo The library is now ready for use with the Python memory protection layer.
echo All operations will run in safe simulation mode.
echo.
pause
exit /b 0

:BuildFailed
echo.
echo ========================================
echo BUILD FAILED
echo ========================================
echo.
echo The C++ libraries could not be built.
echo The Python layer will automatically fall back to simulation mode.
echo This is completely safe and the system will work normally.
echo.
echo To install a compiler:
echo 1. Install Visual Studio Build Tools from Microsoft
echo 2. Or install MinGW-w64 from https://www.mingw-w64.org/
echo 3. Or install MSYS2 from https://www.msys2.org/
echo.
pause
exit /b 1
