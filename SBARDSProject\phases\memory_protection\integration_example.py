#!/usr/bin/env python3
"""
SBARDS Memory Protection Layer - Integration Example
Demonstrates how to integrate the memory protection layer with the main SBARDS system.
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Add memory protection to path
sys.path.insert(0, str(Path(__file__).parent))

class SBARDSMemoryProtectionIntegration:
    """
    Example integration class showing how to incorporate memory protection
    into the main SBARDS system.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the memory protection integration"""
        self.logger = logging.getLogger("SBARDS.MemoryProtection")
        
        # Load configuration
        self.config = self._load_configuration(config_path)
        
        # Initialize memory protection
        self.memory_protection = self._initialize_memory_protection()
        
        # Integration status
        self.integration_active = False
        
        self.logger.info("SBARDS Memory Protection Integration initialized")
    
    def _load_configuration(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """Load memory protection configuration"""
        if config_path is None:
            config_path = "production_config.json"
        
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            self.logger.info(f"Configuration loaded from {config_path}")
            return config
        except Exception as e:
            self.logger.warning(f"Failed to load config from {config_path}: {e}")
            # Return safe default configuration
            return {
                "memory_protection": {
                    "enabled": True,
                    "safe_mode": True,
                    "simulation_only": True,
                    "scan_interval_seconds": 120,
                    "memory_threshold_mb": 200
                },
                "mongodb": {"enabled": False}
            }
    
    def _initialize_memory_protection(self) -> Optional[Any]:
        """Initialize the memory protection layer"""
        try:
            from memory_protection import MemoryProtectionLayer
            
            mp = MemoryProtectionLayer(self.config)
            self.logger.info("Memory protection layer initialized successfully")
            return mp
            
        except Exception as e:
            self.logger.error(f"Failed to initialize memory protection: {e}")
            return None
    
    def activate_protection(self) -> bool:
        """Activate memory protection for the SBARDS system"""
        if not self.memory_protection:
            self.logger.error("Memory protection not available")
            return False
        
        try:
            # Activate advanced protection if available
            if hasattr(self.memory_protection, 'cold_boot_protection'):
                if self.memory_protection.cold_boot_protection:
                    self.memory_protection.cold_boot_protection.activate_protection()
            
            if hasattr(self.memory_protection, 'injection_monitor'):
                if self.memory_protection.injection_monitor:
                    self.memory_protection.injection_monitor.start_monitoring()
            
            self.integration_active = True
            self.logger.info("Memory protection activated for SBARDS system")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to activate memory protection: {e}")
            return False
    
    def deactivate_protection(self) -> bool:
        """Deactivate memory protection"""
        if not self.memory_protection:
            return True
        
        try:
            # Deactivate advanced protection
            if hasattr(self.memory_protection, 'cold_boot_protection'):
                if self.memory_protection.cold_boot_protection:
                    self.memory_protection.cold_boot_protection.deactivate_protection()
            
            if hasattr(self.memory_protection, 'injection_monitor'):
                if self.memory_protection.injection_monitor:
                    self.memory_protection.injection_monitor.stop_monitoring()
            
            self.integration_active = False
            self.logger.info("Memory protection deactivated")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to deactivate memory protection: {e}")
            return False
    
    def scan_process(self, pid: int) -> Optional[Dict[str, Any]]:
        """Scan a specific process using memory protection"""
        if not self.memory_protection:
            return None
        
        try:
            result = self.memory_protection.scan_process_memory(pid)
            if result:
                self.logger.info(f"Process {pid} scanned: {result['status']}")
            return result
            
        except Exception as e:
            self.logger.error(f"Process scan failed for PID {pid}: {e}")
            return None
    
    def scan_all_processes(self) -> Optional[Dict[str, Any]]:
        """Scan all processes using memory protection"""
        if not self.memory_protection:
            return None
        
        try:
            result = self.memory_protection.scan_all_processes()
            if result:
                self.logger.info(f"All processes scanned: {result.get('total_scanned', 0)} processes")
            return result
            
        except Exception as e:
            self.logger.error(f"All processes scan failed: {e}")
            return None
    
    def get_protection_status(self) -> Dict[str, Any]:
        """Get comprehensive protection status"""
        status = {
            "integration_active": self.integration_active,
            "memory_protection_available": self.memory_protection is not None,
            "timestamp": "2024-12-29T00:00:00Z"
        }
        
        if self.memory_protection:
            try:
                mp_status = self.memory_protection.get_protection_status()
                status.update(mp_status)
            except Exception as e:
                status["error"] = str(e)
        
        return status
    
    def encrypt_sensitive_data(self, data: bytes, algorithm: str = "aes-256-gcm") -> Optional[Dict[str, Any]]:
        """Encrypt sensitive data using memory protection"""
        if not self.memory_protection:
            return None
        
        try:
            # Use advanced encryption if available
            if hasattr(self.memory_protection, 'advanced_encryptor'):
                encryptor = self.memory_protection.advanced_encryptor
                if encryptor:
                    from advanced_memory_encryption import EncryptionAlgorithm
                    algo = EncryptionAlgorithm(algorithm)
                    
                    result = encryptor.encrypt_memory_region(
                        start_address=0x10000000,  # Simulated address
                        size=len(data),
                        data=data,
                        algorithm=algo
                    )
                    
                    if result:
                        return {
                            "success": True,
                            "region_id": result.region_id,
                            "algorithm": result.algorithm.value,
                            "encrypted_at": result.encrypted_at.isoformat()
                        }
            
            # Fallback to basic encryption
            result = self.memory_protection.encrypt_memory_partition()
            return result
            
        except Exception as e:
            self.logger.error(f"Data encryption failed: {e}")
            return {"success": False, "error": str(e)}
    
    def emergency_wipe(self) -> Dict[str, Any]:
        """Perform emergency memory wipe"""
        if not self.memory_protection:
            return {"success": False, "error": "Memory protection not available"}
        
        try:
            # Use advanced cold boot protection if available
            if hasattr(self.memory_protection, 'cold_boot_protection'):
                protection = self.memory_protection.cold_boot_protection
                if protection:
                    result = protection.emergency_wipe()
                    return {
                        "success": result.success,
                        "regions_wiped": result.regions_wiped,
                        "bytes_wiped": result.bytes_wiped,
                        "timestamp": result.timestamp.isoformat()
                    }
            
            # Fallback to basic wipe
            result = self.memory_protection.wipe_memory_keys()
            return result
            
        except Exception as e:
            self.logger.error(f"Emergency wipe failed: {e}")
            return {"success": False, "error": str(e)}

def main():
    """Example usage of the integration"""
    print("=" * 60)
    print("SBARDS Memory Protection Integration Example")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Initialize integration
        integration = SBARDSMemoryProtectionIntegration()
        
        # Activate protection
        if integration.activate_protection():
            print("✅ Memory protection activated")
        
        # Get status
        status = integration.get_protection_status()
        print(f"✅ Protection status: {status['integration_active']}")
        
        # Scan current process
        current_pid = os.getpid()
        scan_result = integration.scan_process(current_pid)
        if scan_result:
            print(f"✅ Process scan completed: {scan_result['status']}")
        
        # Test encryption
        test_data = b"This is sensitive SBARDS data"
        encrypt_result = integration.encrypt_sensitive_data(test_data)
        if encrypt_result and encrypt_result.get("success"):
            print("✅ Data encryption successful")
        
        # Deactivate protection
        if integration.deactivate_protection():
            print("✅ Memory protection deactivated")
        
        print("\n🎉 Integration example completed successfully!")
        
    except Exception as e:
        print(f"❌ Integration example failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
