"""
Advanced Memory Encryption Module (6.1.1) - SBARDS Memory Protection Layer
Implements specialized in-memory encryption engine with multiple algorithms,
selective encryption, and secure key management.
"""

import os
import sys
import time
import logging
import secrets
import hashlib
import hmac
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
import ctypes

# Cryptographic dependencies
try:
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives.kdf.scrypt import Scrypt
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.backends import default_backend
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

class EncryptionAlgorithm(Enum):
    """Supported encryption algorithms (6.1.1)"""
    AES_256_GCM = "aes-256-gcm"
    AES_256_CBC = "aes-256-cbc"
    CHACHA20_POLY1305 = "chacha20-poly1305"
    AES_256_XTS = "aes-256-xts"

@dataclass
class EncryptionKey:
    """Secure encryption key with metadata"""
    key_id: str
    algorithm: EncryptionAlgorithm
    key_data: bytes
    iv_nonce: bytes
    created_at: datetime
    expires_at: Optional[datetime]
    usage_count: int = 0
    max_usage: int = 1000

@dataclass
class MemoryRegionEncryption:
    """Encrypted memory region information"""
    region_id: str
    start_address: int
    end_address: int
    size: int
    algorithm: EncryptionAlgorithm
    key_id: str
    encrypted_at: datetime
    integrity_hash: str
    is_sensitive: bool = True

class SecureKeyManager:
    """
    Secure Key Management (6.1.2)
    Implements HSM storage, Shamir's Secret Sharing, and emergency recovery
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("SBARDS.KeyManager")
        
        # Key storage
        self.active_keys: Dict[str, EncryptionKey] = {}
        self.key_rotation_interval = config.get("key_rotation_hours", 24) * 3600
        self.max_key_usage = config.get("max_key_usage", 1000)
        
        # HSM simulation (in production, would use actual HSM)
        self.hsm_available = config.get("hsm_enabled", False)
        self.hsm_simulation = config.get("hsm_simulation", True)
        
        # Shamir's Secret Sharing configuration
        self.shamir_threshold = config.get("shamir_threshold", 3)
        self.shamir_shares = config.get("shamir_shares", 5)
        
        # Key derivation settings
        self.kdf_iterations = config.get("kdf_iterations", 100000)
        self.salt_size = 32
        
        # Thread safety
        self._lock = threading.RLock()
        
        self.logger.info("Secure Key Manager initialized")
    
    def generate_encryption_key(self, algorithm: EncryptionAlgorithm, 
                               sensitive: bool = True) -> EncryptionKey:
        """Generate new encryption key with secure random data"""
        with self._lock:
            key_id = self._generate_key_id()
            
            # Generate key based on algorithm
            if algorithm == EncryptionAlgorithm.AES_256_GCM:
                key_data = secrets.token_bytes(32)  # 256 bits
                iv_nonce = secrets.token_bytes(12)  # 96 bits for GCM
            elif algorithm == EncryptionAlgorithm.AES_256_CBC:
                key_data = secrets.token_bytes(32)  # 256 bits
                iv_nonce = secrets.token_bytes(16)  # 128 bits for CBC
            elif algorithm == EncryptionAlgorithm.CHACHA20_POLY1305:
                key_data = secrets.token_bytes(32)  # 256 bits
                iv_nonce = secrets.token_bytes(12)  # 96 bits for ChaCha20
            elif algorithm == EncryptionAlgorithm.AES_256_XTS:
                key_data = secrets.token_bytes(64)  # 512 bits (two 256-bit keys)
                iv_nonce = secrets.token_bytes(16)  # 128 bits
            else:
                raise ValueError(f"Unsupported algorithm: {algorithm}")
            
            # Create encryption key object
            encryption_key = EncryptionKey(
                key_id=key_id,
                algorithm=algorithm,
                key_data=key_data,
                iv_nonce=iv_nonce,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=self.key_rotation_interval),
                max_usage=self.max_key_usage
            )
            
            # Store in HSM or secure storage
            if self.hsm_available or self.hsm_simulation:
                self._store_key_in_hsm(encryption_key)
            
            self.active_keys[key_id] = encryption_key
            
            self.logger.info(f"Generated encryption key {key_id} using {algorithm.value}")
            return encryption_key
    
    def derive_key_from_password(self, password: str, salt: Optional[bytes] = None,
                                algorithm: EncryptionAlgorithm = EncryptionAlgorithm.AES_256_GCM) -> EncryptionKey:
        """Derive encryption key from password using PBKDF2/Scrypt"""
        if not CRYPTOGRAPHY_AVAILABLE:
            raise RuntimeError("Cryptography library not available")
        
        if salt is None:
            salt = secrets.token_bytes(self.salt_size)
        
        # Use Scrypt for better security against hardware attacks
        kdf = Scrypt(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            n=2**14,  # CPU/memory cost parameter
            r=8,      # Block size parameter
            p=1,      # Parallelization parameter
            backend=default_backend()
        )
        
        key_data = kdf.derive(password.encode('utf-8'))
        
        # Generate IV/nonce
        if algorithm == EncryptionAlgorithm.AES_256_GCM:
            iv_nonce = secrets.token_bytes(12)
        elif algorithm == EncryptionAlgorithm.CHACHA20_POLY1305:
            iv_nonce = secrets.token_bytes(12)
        else:
            iv_nonce = secrets.token_bytes(16)
        
        key_id = self._generate_key_id()
        
        encryption_key = EncryptionKey(
            key_id=key_id,
            algorithm=algorithm,
            key_data=key_data,
            iv_nonce=iv_nonce,
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(seconds=self.key_rotation_interval)
        )
        
        self.active_keys[key_id] = encryption_key
        
        self.logger.info(f"Derived encryption key {key_id} from password")
        return encryption_key
    
    def rotate_key(self, key_id: str) -> Optional[EncryptionKey]:
        """Rotate encryption key (6.1.2)"""
        with self._lock:
            if key_id not in self.active_keys:
                return None
            
            old_key = self.active_keys[key_id]
            
            # Generate new key with same algorithm
            new_key = self.generate_encryption_key(old_key.algorithm)
            
            # Securely wipe old key
            self._secure_wipe_key(old_key)
            
            # Remove old key
            del self.active_keys[key_id]
            
            self.logger.info(f"Rotated key {key_id} -> {new_key.key_id}")
            return new_key
    
    def _generate_key_id(self) -> str:
        """Generate unique key identifier"""
        timestamp = int(time.time() * 1000000)  # Microsecond precision
        random_part = secrets.token_hex(8)
        return f"key_{timestamp}_{random_part}"
    
    def _store_key_in_hsm(self, key: EncryptionKey):
        """Store key in Hardware Security Module (simulated)"""
        if self.hsm_simulation:
            # Simulate HSM storage with additional security
            self.logger.debug(f"Simulated HSM storage for key {key.key_id}")
        else:
            # In production, integrate with actual HSM
            pass
    
    def _secure_wipe_key(self, key: EncryptionKey):
        """Securely wipe key from memory (DoD 5220.22-M compliant)"""
        # Overwrite key data multiple times
        if hasattr(key.key_data, '__len__'):
            key_length = len(key.key_data)
            # Three-pass wipe: 0x00, 0xFF, random
            key.key_data = b'\x00' * key_length
            key.key_data = b'\xFF' * key_length
            key.key_data = secrets.token_bytes(key_length)
            key.key_data = b'\x00' * key_length
        
        # Wipe IV/nonce
        if hasattr(key.iv_nonce, '__len__'):
            iv_length = len(key.iv_nonce)
            key.iv_nonce = b'\x00' * iv_length
            key.iv_nonce = b'\xFF' * iv_length
            key.iv_nonce = secrets.token_bytes(iv_length)
            key.iv_nonce = b'\x00' * iv_length

class AdvancedMemoryEncryptor:
    """
    Advanced Memory Encryption Engine (6.1.1)
    Implements selective encryption of sensitive memory areas with multiple algorithms
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("SBARDS.MemoryEncryptor")
        
        # Initialize key manager
        self.key_manager = SecureKeyManager(config.get("key_management", {}))
        
        # Encryption settings
        self.default_algorithm = EncryptionAlgorithm(
            config.get("default_algorithm", "aes-256-gcm")
        )
        self.selective_encryption = config.get("selective_encryption", True)
        self.max_region_size = config.get("max_region_size_mb", 100) * 1024 * 1024
        
        # Encrypted regions tracking
        self.encrypted_regions: Dict[str, MemoryRegionEncryption] = {}
        
        # Thread safety
        self._lock = threading.RLock()
        
        self.logger.info("Advanced Memory Encryptor initialized")
    
    def encrypt_memory_region(self, start_address: int, size: int, 
                            data: bytes, algorithm: Optional[EncryptionAlgorithm] = None,
                            sensitive: bool = True) -> Optional[MemoryRegionEncryption]:
        """
        Encrypt specific memory region (6.1.1)
        """
        if not CRYPTOGRAPHY_AVAILABLE:
            return self._simulate_encryption(start_address, size, data, algorithm, sensitive)
        
        with self._lock:
            try:
                # Validate inputs
                if size > self.max_region_size:
                    raise ValueError(f"Region size {size} exceeds maximum {self.max_region_size}")
                
                if len(data) != size:
                    raise ValueError("Data size doesn't match specified region size")
                
                # Use default algorithm if not specified
                if algorithm is None:
                    algorithm = self.default_algorithm
                
                # Generate encryption key
                encryption_key = self.key_manager.generate_encryption_key(algorithm, sensitive)
                
                # Encrypt data based on algorithm
                encrypted_data, auth_tag = self._encrypt_data(data, encryption_key)
                
                # Calculate integrity hash
                integrity_hash = hashlib.sha256(encrypted_data).hexdigest()
                
                # Create region encryption record
                region_id = self._generate_region_id(start_address, size)
                region_encryption = MemoryRegionEncryption(
                    region_id=region_id,
                    start_address=start_address,
                    end_address=start_address + size,
                    size=size,
                    algorithm=algorithm,
                    key_id=encryption_key.key_id,
                    encrypted_at=datetime.now(),
                    integrity_hash=integrity_hash,
                    is_sensitive=sensitive
                )
                
                self.encrypted_regions[region_id] = region_encryption
                
                self.logger.info(f"Encrypted memory region {region_id} at 0x{start_address:x} ({size} bytes)")
                return region_encryption
                
            except Exception as e:
                self.logger.error(f"Memory encryption failed: {e}")
                return None
    
    def _encrypt_data(self, data: bytes, key: EncryptionKey) -> Tuple[bytes, Optional[bytes]]:
        """Encrypt data using specified algorithm"""
        if key.algorithm == EncryptionAlgorithm.AES_256_GCM:
            cipher = Cipher(
                algorithms.AES(key.key_data),
                modes.GCM(key.iv_nonce),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            encrypted_data = encryptor.update(data) + encryptor.finalize()
            return encrypted_data, encryptor.tag
            
        elif key.algorithm == EncryptionAlgorithm.CHACHA20_POLY1305:
            cipher = Cipher(
                algorithms.ChaCha20(key.key_data, key.iv_nonce),
                None,
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            encrypted_data = encryptor.update(data) + encryptor.finalize()
            return encrypted_data, None
            
        elif key.algorithm == EncryptionAlgorithm.AES_256_CBC:
            cipher = Cipher(
                algorithms.AES(key.key_data),
                modes.CBC(key.iv_nonce),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            # Pad data to block size
            padded_data = self._pad_data(data, 16)
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            return encrypted_data, None
            
        else:
            raise ValueError(f"Unsupported encryption algorithm: {key.algorithm}")
    
    def _simulate_encryption(self, start_address: int, size: int, data: bytes,
                           algorithm: Optional[EncryptionAlgorithm], sensitive: bool) -> MemoryRegionEncryption:
        """Simulate encryption when cryptography library not available"""
        region_id = self._generate_region_id(start_address, size)
        algorithm = algorithm or self.default_algorithm
        
        # Simulate key generation
        key_id = f"sim_key_{secrets.token_hex(8)}"
        
        # Simulate encryption
        simulated_hash = hashlib.sha256(data).hexdigest()
        
        region_encryption = MemoryRegionEncryption(
            region_id=region_id,
            start_address=start_address,
            end_address=start_address + size,
            size=size,
            algorithm=algorithm,
            key_id=key_id,
            encrypted_at=datetime.now(),
            integrity_hash=simulated_hash,
            is_sensitive=sensitive
        )
        
        self.encrypted_regions[region_id] = region_encryption
        self.logger.info(f"Simulated encryption for region {region_id}")
        
        return region_encryption
    
    def _generate_region_id(self, start_address: int, size: int) -> str:
        """Generate unique region identifier"""
        timestamp = int(time.time() * 1000000)
        return f"region_{start_address:x}_{size}_{timestamp}"
    
    def _pad_data(self, data: bytes, block_size: int) -> bytes:
        """PKCS7 padding for block ciphers"""
        padding_length = block_size - (len(data) % block_size)
        padding = bytes([padding_length] * padding_length)
        return data + padding
