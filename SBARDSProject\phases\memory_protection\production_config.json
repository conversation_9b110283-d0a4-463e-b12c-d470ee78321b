{"memory_protection": {"enabled": true, "safe_mode": true, "simulation_only": true, "scan_interval_seconds": 120, "memory_threshold_mb": 200, "max_processes_per_scan": 1000, "rate_limit_minutes": 1, "suspicious_patterns": ["temp_malware", "crypto_miner", "suspicious_script"], "high_risk_threshold": 0.7, "medium_risk_threshold": 0.4, "memory_dump_enabled": true, "memory_dump_directory": "memory_dumps", "max_dump_size_mb": 100}, "mongodb": {"enabled": false, "uri": "mongodb://localhost:27017", "database": "sbards_production", "collection": "memory_scans", "ssl": false, "tls_insecure": false, "connection_timeout_ms": 5000, "socket_timeout_ms": 3000, "max_pool_size": 10}, "api_server": {"enabled": true, "host": "127.0.0.1", "port": 8000, "api_key": "sbards-secure-key-2024", "rate_limits": {"encrypt": "5/minute", "wipe": "5/minute", "status": "10/minute", "scan": "3/minute"}, "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"], "https_redirect": false, "log_level": "INFO"}, "ml_models": {"behavior_analysis": {"enabled": true, "model_path": "python/ml_models/behavior_model.pkl", "scaler_path": "python/ml_models/scaler.pkl", "fallback_to_heuristic": true, "feature_count": 7, "contamination": 0.1, "n_estimators": 100}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "memory_protection.log", "max_file_size_mb": 10, "backup_count": 5, "console_output": true}, "security": {"input_validation": true, "output_sanitization": true, "rate_limiting": true, "authentication_required": true, "safe_mode_enforced": true, "simulation_mode_default": true}, "performance": {"max_concurrent_scans": 5, "process_timeout_seconds": 30, "memory_limit_mb": 512, "cpu_limit_percent": 50, "cache_results": true, "cache_ttl_seconds": 300}, "platform_specific": {"windows": {"device_paths": {"memory_partition": "C:\\SBARDS\\memory_partition", "memory_keys": "C:\\SBARDS\\memory_keys"}, "dump_method": "windows_simulation", "admin_required": false}, "linux": {"device_paths": {"memory_partition": "/dev/sdb1", "memory_keys": "/dev/mapper/sbards_mem"}, "dump_method": "gcore", "admin_required": false}}, "alerts": {"high_memory_usage_threshold": 70, "high_cpu_usage_threshold": 80, "suspicious_process_alert": true, "rapid_memory_growth_alert": true, "alert_cooldown_seconds": 300}, "cleanup": {"auto_cleanup_enabled": true, "cleanup_interval_hours": 24, "max_log_age_days": 7, "max_dump_age_days": 3, "temp_file_cleanup": true}, "memory_analysis": {"enable_volatility": false, "enable_yara": false, "max_image_size_mb": 500, "capture_directory": "memory_images", "auto_analysis": true, "analysis_timeout_seconds": 300, "keep_images": false}, "memory_encryption": {"default_algorithm": "aes-256-gcm", "selective_encryption": true, "max_region_size_mb": 100, "auto_encrypt_sensitive": true, "encryption_strength": "high"}, "key_management": {"key_rotation_hours": 24, "max_key_usage": 1000, "hsm_enabled": false, "hsm_simulation": true, "shamir_threshold": 3, "shamir_shares": 5, "kdf_iterations": 100000, "emergency_recovery": true}, "cold_boot_protection": {"auto_wipe_on_shutdown": true, "emergency_wipe_enabled": true, "key_rotation_hours": 1, "protection_level": "high"}, "key_scrambling": {"scramble_interval_seconds": 30, "scatter_locations": 16, "xor_rounds": 3, "periodic_scrambling": true}, "memory_wiping": {"default_wipe_method": "three_pass_dod", "verify_wipe": true, "max_wipe_size_mb": 100, "simulation_mode": true, "use_mlock": true, "use_vectorized": true}, "injection_monitoring": {"monitoring_enabled": true, "scan_interval_seconds": 5, "max_events_per_process": 1000, "suspicious_threshold": 0.7, "rop_detection": true, "behavioral_analysis": true, "real_time_alerts": true}, "advanced_protection": {"dep_enforcement": true, "aslr_enhancement": true, "shared_memory_monitoring": true, "critical_area_protection": true, "anti_debugging": true, "anti_emulation": true, "memory_obfuscation": true}, "integration": {"os_integration": true, "kernel_privileges": false, "system_wide_policies": true, "threat_intelligence_sharing": true, "dynamic_analysis_integration": true, "threat_response_integration": true, "monitoring_system_integration": true}}