"""
Memory Protection Layer for SBARDS - Enhanced Version
"""

import os
import sys
import time
import logging
import psutil
import hashlib
import ctypes
import platform
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
try:
    from pymongo import MongoClient
    from pymongo.errors import PyMongoError
    PYMONGO_AVAILABLE = True
except ImportError:
    MongoClient = None
    PyMongoError = Exception
    PYMONGO_AVAILABLE = False
from sklearn.ensemble import IsolationForest
import joblib

# Load C++ memory protection library
try:
    if platform.system() == 'Linux':
        lib_path = os.path.abspath('./phases/memory_protection/cpp/libmemprotect.so')
        libmemprotect = ctypes.CDLL(lib_path)
    elif platform.system() == 'Windows':
        lib_path = os.path.abspath('./phases/memory_protection/cpp/libmemprotect.dll')
        libmemprotect = ctypes.CDLL(lib_path)
    else:
        raise OSError("Unsupported operating system")

    libmemprotect.encrypt_volume.argtypes = [ctypes.c_char_p]
    libmemprotect.encrypt_volume.restype = ctypes.c_int
    libmemprotect.wipe_memory_keys.argtypes = [ctypes.c_char_p]
    libmemprotect.wipe_memory_keys.restype = ctypes.c_int

except Exception as e:
    logging.getLogger("SBARDS.MemoryProtection").warning(
        f"Failed to load memory protection library: {e}. Running in simulation mode.")
    libmemprotect = None

class MemoryProtectionLayer:
    """
    Enhanced Memory Protection Layer with all required improvements
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("SBARDS.MemoryProtection")
        self.logger.addHandler(logging.NullHandler())

        # Memory protection settings
        self.memory_config = config.get("memory_protection", {})
        self.scan_interval = max(60, self.memory_config.get("scan_interval_seconds", 60))
        self.memory_threshold = max(100, self.memory_config.get("memory_threshold_mb", 100))
        self.suspicious_patterns = self._sanitize_patterns(
            self.memory_config.get("suspicious_patterns", []))

        # Process monitoring
        self.monitored_processes = set()
        self.suspicious_processes = []
        self.analysis_results = []
        self.last_full_scan = datetime.min
        self.scanned_pids = set()

        # Initialize ML model
        self.ml_model = self._load_ml_model()

        # Initialize MongoDB connection
        self._init_mongodb()

        self.logger.info("Memory Protection Layer initialized with all enhancements")

    def _init_mongodb(self):
        """Initialize MongoDB connection securely"""
        self.mongo_config = self.config.get("mongodb", {})
        self.mongo_client = None
        self.mongo_db = None
        self.mongo_collection = None

        if not PYMONGO_AVAILABLE:
            self.logger.info("MongoDB not available - running without database storage")
            return

        if self.mongo_config.get("enabled", False):
            try:
                self.mongo_client = MongoClient(
                    self.mongo_config.get("uri", "mongodb://localhost:27017"),
                    serverSelectionTimeoutMS=5000,
                    connectTimeoutMS=3000,
                    socketTimeoutMS=3000,
                    maxPoolSize=10,
                    ssl=self.mongo_config.get("ssl", False),
                    tlsInsecure=self.mongo_config.get("tls_insecure", False)
                )
                self.mongo_db = self.mongo_client.get_database(
                    self.mongo_config.get("database", "sbards")
                )
                self.mongo_collection = self.mongo_db.get_collection(
                    self.mongo_config.get("collection", "memory_scans")
                )
                self.mongo_client.admin.command('ping')
                self.logger.info("MongoDB connection established")
            except Exception as e:
                self.logger.warning(f"MongoDB connection failed: {str(e)[:100]}")

    def _load_ml_model(self):
        """Load the behavioral analysis ML model"""
        try:
            model_path = os.path.join(os.path.dirname(__file__), 'ml_models', 'behavior_model.pkl')
            return joblib.load(model_path)
        except Exception as e:
            self.logger.warning(f"Failed to load ML model: {e}. Using default heuristic")
            return None

    def _extract_features(self, process_info: Dict) -> List[float]:
        """Extract features for behavioral analysis"""
        return [
            process_info.get('cpu_percent', 0),
            process_info.get('memory_percent', 0),
            process_info.get('num_connections', 0),
            process_info.get('num_threads', 0),
            process_info.get('num_handles', 0),
            process_info.get('io_counters', (0, 0))[0],
            process_info.get('io_counters', (0, 0))[1]
        ]

    def evaluate_behavior(self, process_info: Dict) -> float:
        """Evaluate process behavior using ML"""
        try:
            if not self.ml_model:
                return 0.0

            features = self._extract_features(process_info)
            risk_score = self.ml_model.predict_proba([features])[0][1]
            return float(risk_score)
        except Exception as e:
            self.logger.error(f"Behavior evaluation failed: {e}")
            return 0.0

    def _sanitize_patterns(self, patterns: List[str]) -> List[str]:
        """Sanitize input patterns to prevent injection attacks"""
        return [p.strip().lower() for p in patterns if p and isinstance(p, str)]

    def encrypt_memory_partition(self) -> Dict[str, Any]:
        """Encrypt memory partition with secure implementation - Windows compatible"""
        result = {
            "success": False,
            "message": "",
            "simulated": libmemprotect is None,
            "timestamp": datetime.now().isoformat(),
            "platform": platform.system()
        }

        try:
            if libmemprotect:
                # Use platform-appropriate device paths
                if platform.system() == 'Windows':
                    device_path = b"C:\\SBARDS\\memory_partition"
                else:
                    device_path = b"/dev/sdb1"

                if not isinstance(device_path, bytes):
                    device_path = device_path.encode('utf-8')

                ret = libmemprotect.encrypt_volume(device_path)
                if ret == 0:
                    result.update({
                        "success": True,
                        "message": f"Memory partition encrypted securely on {platform.system()}"
                    })
                else:
                    result["message"] = f"Encryption failed (C++ error code: {ret})"
            else:
                # Enhanced simulation mode with Windows-specific messaging
                result.update({
                    "success": True,
                    "message": f"Simulated memory encryption completed on {platform.system()} (Safe Mode)"
                })
                self.logger.info("Running in safe simulation mode - no actual system changes")

            self.logger.info("Memory encryption: %s", result["message"])
        except Exception as e:
            result["message"] = "Secure operation failed"
            self.logger.error("Encryption error: %s", str(e)[:100])

        return result

    def wipe_memory_keys(self) -> Dict[str, Any]:
        """Securely wipe memory keys - Windows compatible"""
        result = {
            "success": False,
            "message": "",
            "simulated": libmemprotect is None,
            "timestamp": datetime.now().isoformat(),
            "platform": platform.system()
        }

        try:
            if libmemprotect:
                # Use platform-appropriate mapper paths
                if platform.system() == 'Windows':
                    mapper_path = b"C:\\SBARDS\\memory_keys"
                else:
                    mapper_path = b"/dev/mapper/sbards_mem"

                if not isinstance(mapper_path, bytes):
                    mapper_path = mapper_path.encode('utf-8')

                ret = libmemprotect.wipe_memory_keys(mapper_path)
                if ret == 0:
                    result.update({
                        "success": True,
                        "message": f"Memory keys securely wiped on {platform.system()}"
                    })
                else:
                    result["message"] = f"Wipe failed (C++ error code: {ret})"
            else:
                # Enhanced simulation mode with Windows-specific messaging
                result.update({
                    "success": True,
                    "message": f"Simulated memory wipe completed on {platform.system()} (Safe Mode)"
                })
                self.logger.info("Running in safe simulation mode - no actual system changes")

            self.logger.info("Memory wipe: %s", result["message"])
        except Exception as e:
            result["message"] = "Secure operation failed"
            self.logger.error("Wipe error: %s", str(e)[:100])

        return result

    def create_memory_dump(self, pid: int, output_dir: str = "memory_dumps") -> Dict[str, Any]:
        """Create memory dump with multiple fallback methods - Windows compatible"""
        results = {
            "pid": pid,
            "timestamp": datetime.now().isoformat(),
            "dump_file": None,
            "success": False,
            "method": None,
            "platform": platform.system()
        }

        try:
            os.makedirs(output_dir, exist_ok=True)
            process = psutil.Process(pid)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dump_filename = f"memory_dump_{process.name()}_{pid}_{timestamp}.dmp"
            dump_path = os.path.join(output_dir, dump_filename)

            # Windows-specific methods
            if platform.system() == 'Windows':
                # Method 1: Try using Windows Task Manager dump (safe simulation)
                try:
                    self.logger.info(f"Windows memory dump simulation for PID {pid}")
                    # In real implementation, would use Windows APIs like MiniDumpWriteDump
                    # For safety, we create a detailed process info dump instead
                    results.update({
                        "success": True,
                        "dump_file": dump_path,
                        "method": "windows_simulation"
                    })
                    self.logger.info("Windows memory dump simulation completed safely")
                except Exception as e:
                    self.logger.debug(f"Windows dump simulation failed: {e}")

            # Linux-specific methods
            elif platform.system() == 'Linux':
                # Method 1: Try gcore on Linux
                try:
                    import subprocess
                    subprocess.run(['gcore', '-o', dump_path, str(pid)], check=True)
                    results.update({
                        "success": True,
                        "dump_file": f"{dump_path}.{pid}",
                        "method": "gcore"
                    })
                    return results
                except Exception as e:
                    self.logger.debug(f"gcore failed: {e}")

                # Method 2: Try reading from /proc/pid/mem
                try:
                    mem_path = f"/proc/{pid}/mem"
                    if os.path.exists(mem_path):
                        with open(mem_path, 'rb') as mem_file, open(dump_path, 'wb') as dump_file:
                            dump_file.write(mem_file.read())
                        results.update({
                            "success": True,
                            "dump_file": dump_path,
                            "method": "/proc/pid/mem"
                        })
                        return results
                except Exception as e:
                    self.logger.debug(f"/proc/{pid}/mem read failed: {e}")

            # Enhanced fallback: Create detailed process information dump
            dump_info = {
                "pid": pid,
                "process_name": process.name(),
                "memory_info": process.memory_info()._asdict(),
                "create_time": process.create_time(),
                "dump_timestamp": timestamp,
                "platform": platform.system(),
                "cmdline": process.cmdline() if process.cmdline() else [],
                "exe": process.exe() if process.exe() else "unknown",
                "status": process.status(),
                "note": f"Safe process information dump for {platform.system()} - no actual memory content"
            }

            with open(dump_path, 'w') as f:
                import json
                json.dump(dump_info, f, indent=2)
            results.update({
                "success": True,
                "dump_file": dump_path,
                "method": f"safe_info_dump_{platform.system().lower()}"
            })

        except Exception as e:
            self.logger.error(f"Memory dump failed for PID {pid}: {e}")
            results["error"] = str(e)

        return results

    def _store_scan_result(self, scan_result: Dict[str, Any]) -> bool:
        """Securely store scan results in MongoDB"""
        if not self.mongo_collection:
            return False

        try:
            sanitized = {
                "pid": int(scan_result.get("pid", 0)),
                "timestamp": datetime.now(),
                "status": str(scan_result.get("status", "unknown"))[:50],
                "risk_score": min(max(int(scan_result.get("risk_score", 0)), 0), 100),
                "process_name": str(scan_result.get("process_info", {}).get("name", ""))[:100],
                "patterns": [
                    {"type": str(p.get("type"))[:20], "severity": str(p.get("severity"))[:10]}
                    for p in scan_result.get("suspicious_patterns", [])[:10]
                ]
            }

            result = self.mongo_collection.insert_one(sanitized)
            return result.acknowledged
        except PyMongoError as e:
            self.logger.warning("Secure storage failed: %s", str(e)[:100])
            return False

    def scan_all_processes(self) -> Dict[str, Any]:
        """Scan all processes with rate limiting and cleanup"""
        if datetime.now() - self.last_full_scan < timedelta(minutes=1):
            self.logger.warning("Scan rate limited. Skipping this scan cycle.")
            return {"error": "Rate limited", "timestamp": datetime.now().isoformat()}

        self.last_full_scan = datetime.now()
        self.scanned_pids = set()

        results = {
            "timestamp": self.last_full_scan.isoformat(),
            "total_processes": 0,
            "scanned_processes": 0,
            "suspicious_processes": [],
            "high_risk_processes": [],
            "system_memory": {},
            "summary": {}
        }

        try:
            memory = psutil.virtual_memory()
            results["system_memory"] = {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "free": memory.free,
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3)
            }

            processes = list(psutil.process_iter(['pid', 'name', 'memory_percent']))
            results["total_processes"] = len(processes)

            for proc in processes:
                try:
                    pid = proc.info['pid']

                    if pid in self.scanned_pids:
                        continue
                    self.scanned_pids.add(pid)

                    if pid in [0, 4]:  # Skip system processes
                        continue

                    if proc.info['memory_percent'] > 70:
                        self.logger.warning(
                            f"High memory usage by PID {pid}: {proc.info['memory_percent']}%"
                        )

                    scan_result = self.scan_process_memory(pid)
                    results["scanned_processes"] += 1

                    if scan_result.get("risk_score", 0) > 0:
                        results["suspicious_processes"].append(scan_result)

                    if scan_result.get("status") == "high_risk":
                        results["high_risk_processes"].append(scan_result)

                except Exception as e:
                    self.logger.debug(f"Error scanning process {proc.info.get('pid', 'unknown')}: {e}")
                    continue

            results["summary"] = {
                "suspicious_count": len(results["suspicious_processes"]),
                "high_risk_count": len(results["high_risk_processes"]),
                "scan_completion_rate": (results["scanned_processes"] / results["total_processes"]) * 100
            }

            self.logger.info(f"Memory scan completed: {results['scanned_processes']}/{results['total_processes']} processes scanned")

            # Clear temporary data
            self.suspicious_processes = results["suspicious_processes"].copy()
            self.analysis_results = []

        except Exception as e:
            self.logger.error(f"Error during comprehensive memory scan: {e}")
            results["error"] = str(e)

        return results

    def scan_process_memory(self, pid: int) -> Dict[str, Any]:
        """Scan process memory with behavioral analysis"""
        if pid in self.scanned_pids:
            return {"pid": pid, "status": "already_scanned"}

        results = {
            "pid": pid,
            "timestamp": datetime.now().isoformat(),
            "memory_usage": {},
            "suspicious_patterns": [],
            "risk_score": 0,
            "status": "unknown"
        }

        try:
            process = psutil.Process(pid)
            process_info = {
                "name": process.name(),
                "exe": process.exe() if process.exe() else "unknown",
                "cmdline": " ".join(process.cmdline()) if process.cmdline() else "unknown",
                "create_time": process.create_time(),
                "status": process.status(),
                "cpu_percent": process.cpu_percent(),
                "memory_percent": process.memory_percent(),
                "num_connections": len(process.connections()),
                "num_threads": process.num_threads(),
                "num_handles": process.num_handles(),
                "io_counters": process.io_counters(),
                "memory_info": process.memory_info()._asdict()
            }

            results["process_info"] = process_info
            results["memory_usage"] = {
                "rss": process_info["memory_info"]["rss"],
                "vms": process_info["memory_info"]["vms"],
                "percent": process_info["memory_percent"],
                "rss_mb": process_info["memory_info"]["rss"] / (1024 * 1024),
                "vms_mb": process_info["memory_info"]["vms"] / (1024 * 1024)
            }

            # Behavioral analysis
            behavior_score = self.evaluate_behavior(process_info)
            if behavior_score > 0.7:
                results["risk_score"] += int(behavior_score * 40)
                results["suspicious_patterns"].append({
                    "type": "suspicious_behavior",
                    "description": f"Suspicious behavior (score: {behavior_score:.2f})",
                    "severity": "high"
                })

            # Other checks (existing functionality)
            if process_info['memory_percent'] > 50:
                results["risk_score"] += 30
                results["suspicious_patterns"].append({
                    "type": "high_memory_usage",
                    "description": f"High memory usage ({process_info['memory_percent']:.1f}%)",
                    "severity": "medium"
                })

            if self._check_rapid_memory_growth(pid):
                results["risk_score"] += 50
                results["suspicious_patterns"].append({
                    "type": "rapid_memory_growth",
                    "description": "Rapid memory allocation",
                    "severity": "high"
                })

            if self._is_suspicious_process(process_info):
                results["risk_score"] += 40
                results["suspicious_patterns"].append({
                    "type": "suspicious_process",
                    "description": "Suspicious process characteristics",
                    "severity": "high"
                })

            # Determine overall status
            if results["risk_score"] >= 70:
                results["status"] = "high_risk"
            elif results["risk_score"] >= 40:
                results["status"] = "medium_risk"
            elif results["risk_score"] > 0:
                results["status"] = "low_risk"
            else:
                results["status"] = "clean"

            # Store result in MongoDB
            if self.mongo_collection:
                self._store_scan_result(results)

            self.logger.debug(f"Memory scan completed for PID {pid}: {results['status']}")

        except psutil.NoSuchProcess:
            results["error"] = "Process not found"
            self.logger.warning(f"Process {pid} not found during memory scan")
        except psutil.AccessDenied:
            results["error"] = "Access denied"
            self.logger.warning(f"Access denied for process {pid}")
        except Exception as e:
            results["error"] = str(e)
            self.logger.error(f"Error scanning memory for PID {pid}: {e}")

        return results

    def _check_rapid_memory_growth(self, pid: int) -> bool:
        """Check for rapid memory growth (placeholder implementation)"""
        try:
            process = psutil.Process(pid)
            return process.memory_percent() > 20
        except Exception:
            return False

    def _is_suspicious_process(self, process_info: Dict[str, Any]) -> bool:
        """Check for suspicious process characteristics"""
        try:
            name = process_info.get("name", "").lower()
            exe = process_info.get("exe", "").lower()
            cmdline = process_info.get("cmdline", "").lower()

            suspicious_names = [
                "cryptolocker", "wannacry", "petya", "notpetya", "ryuk",
                "maze", "sodinokibi", "revil", "darkside", "conti"
            ]

            for suspicious_name in suspicious_names:
                if suspicious_name in name or suspicious_name in exe:
                    return True

            suspicious_patterns = [
                "vssadmin delete shadows",
                "wbadmin delete catalog",
                "bcdedit /set {default} bootstatuspolicy ignoreallfailures",
                "bcdedit /set {default} recoveryenabled no",
                "wmic shadowcopy delete",
                "cipher /w:"
            ]

            for pattern in suspicious_patterns:
                if pattern in cmdline:
                    return True

            return False
        except Exception:
            return False

    def get_protection_status(self) -> Dict[str, Any]:
        """Get current memory protection status"""
        try:
            memory = psutil.virtual_memory()

            return {
                "enabled": True,
                "scan_interval": self.scan_interval,
                "memory_threshold": self.memory_threshold,
                "system_memory": {
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "usage_percent": memory.percent
                },
                "monitored_processes": len(self.monitored_processes),
                "suspicious_processes": len(self.suspicious_processes),
                "last_scan": self.last_full_scan.isoformat(),
                "mongo_connected": self.mongo_client is not None,
                "ml_model_loaded": self.ml_model is not None
            }
        except Exception as e:
            self.logger.error(f"Error getting protection status: {e}")
            return {"enabled": False, "error": str(e)}