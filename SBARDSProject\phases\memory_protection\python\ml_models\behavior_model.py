"""
Behavioral Analysis Model for Memory Protection

This module implements machine learning based behavioral analysis
of processes for memory protection layer.
"""

try:
    import numpy as np
    from sklearn.ensemble import IsolationForest
    from sklearn.preprocessing import StandardScaler
    import joblib
    SKLEARN_AVAILABLE = True
except ImportError:
    # Fallback implementations
    SKLEARN_AVAILABLE = False

    class MockIsolationForest:
        def __init__(self, *args, **kwargs):
            pass
        def fit(self, X):
            pass
        def decision_function(self, X):
            return [0.0] * len(X)

    class MockStandardScaler:
        def __init__(self):
            pass
        def transform(self, X):
            return X

    IsolationForest = MockIsolationForest
    StandardScaler = MockStandardScaler

    def joblib_load(path):
        return MockIsolationForest()

    def joblib_dump(obj, path):
        pass

    joblib = type('MockJoblib', (), {'load': joblib_load, 'dump': joblib_dump})()

import os

class BehaviorModel:
    def __init__(self):
        self.model = self._load_model()
        self.scaler = StandardScaler()

    def _load_model(self):
        """Load pre-trained model or create default"""
        model_path = os.path.join(os.path.dirname(__file__), 'behavior_model.pkl')
        try:
            return joblib.load(model_path)
        except:
            # Default model if trained model not available
            return IsolationForest(n_estimators=100, contamination=0.1)

    def evaluate(self, features: list) -> float:
        """
        Evaluate process behavior and return risk score (0.0-1.0)

        Args:
            features: List of process features
                      [cpu_percent, memory_percent, num_connections,
                       num_threads, num_handles, read_io, write_io]

        Returns:
            float: Risk score between 0.0 (normal) and 1.0 (malicious)
        """
        try:
            if len(features) != 7:
                return 0.0

            # Normalize features
            features = self.scaler.transform([features])

            # Get anomaly score (0=normal, 1=anomaly)
            score = self.model.decision_function([features])[0]

            # Convert to 0-1 range (higher=more risky)
            risk_score = (score + 0.5) * 1.5  # Adjusted scaling
            return max(0.0, min(1.0, risk_score))

        except Exception as e:
            print(f"Evaluation error: {e}")
            return 0.0

# Utility function to train and save the model
def train_and_save_model():
    from sklearn.datasets import make_blobs
    from sklearn.ensemble import IsolationForest

    # Generate sample data (in real use, use actual process data)
    X, _ = make_blobs(n_samples=1000, centers=1, n_features=7, random_state=42)

    # Train model
    model = IsolationForest(n_estimators=100, contamination=0.1)
    model.fit(X)

    # Save model
    joblib.dump(model, 'behavior_model.pkl')
    print("Model trained and saved successfully")

if __name__ == "__main__":
    train_and_save_model()