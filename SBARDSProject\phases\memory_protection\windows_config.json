{"memory_protection": {"enabled": true, "safe_mode": true, "simulation_only": true, "platform": "windows", "scan_interval_seconds": 120, "memory_threshold_mb": 200, "max_processes_per_scan": 50, "rate_limiting": {"enabled": true, "max_scans_per_minute": 5, "cooldown_seconds": 60}, "windows_specific": {"use_wmi": true, "use_performance_counters": true, "safe_memory_access": true, "no_system_modification": true}, "suspicious_patterns": ["high_memory_usage", "rapid_memory_growth", "suspicious_process_names", "ransomware_indicators"], "security": {"input_validation": true, "output_sanitization": true, "error_handling": "safe", "logging_level": "info"}}, "mongodb": {"enabled": false, "uri": "mongodb://localhost:27017", "database": "sbards_memory", "collection": "scan_results", "ssl": false, "connection_timeout": 5000, "socket_timeout": 3000}, "api_server": {"enabled": true, "host": "127.0.0.1", "port": 8080, "ssl": false, "cors_enabled": true, "rate_limiting": {"enabled": true, "requests_per_minute": 60}, "authentication": {"required": true, "api_key_header": "X-API-KEY"}}, "logging": {"level": "INFO", "file": "logs/memory_protection.log", "max_size_mb": 10, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "safety_checks": {"verify_simulation_mode": true, "prevent_system_access": true, "validate_all_inputs": true, "sanitize_all_outputs": true, "enable_error_recovery": true}}