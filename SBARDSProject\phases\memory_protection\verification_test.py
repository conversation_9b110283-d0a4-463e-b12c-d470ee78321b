#!/usr/bin/env python3
"""
SBARDS Memory Protection Layer - Comprehensive Verification Test
Verifies all components are working correctly before deployment.
"""

import os
import sys
import platform
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

print("=" * 80)
print("🔍 SBARDS MEMORY PROTECTION LAYER - VERIFICATION TEST")
print("=" * 80)
print(f"Platform: {platform.system()} {platform.release()}")
print(f"Python: {sys.version}")
print(f"Working Directory: {os.getcwd()}")
print("=" * 80)

def test_core_imports():
    """Test core module imports"""
    print("\n📦 Testing Core Module Imports...")
    
    try:
        import psutil
        print("✅ psutil - OK")
    except ImportError as e:
        print(f"❌ psutil - FAILED: {e}")
        return False
    
    try:
        from memory_protection import MemoryProtectionLayer
        print("✅ MemoryProtectionLayer - OK")
    except ImportError as e:
        print(f"❌ MemoryProtectionLayer - FAILED: {e}")
        return False
    
    return True

def test_advanced_imports():
    """Test advanced module imports"""
    print("\n📦 Testing Advanced Module Imports...")
    
    modules = [
        ("advanced_memory_analysis", "AdvancedMemoryAnalyzer"),
        ("advanced_memory_encryption", "AdvancedMemoryEncryptor"),
        ("cold_boot_protection", "ColdBootProtectionSystem"),
        ("injection_monitoring", "MemoryInjectionMonitor")
    ]
    
    success_count = 0
    for module_name, class_name in modules:
        try:
            module = __import__(module_name)
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name} - OK")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name}.{class_name} - FAILED: {e}")
        except AttributeError as e:
            print(f"❌ {module_name}.{class_name} - FAILED: {e}")
        except Exception as e:
            print(f"❌ {module_name}.{class_name} - FAILED: {e}")
    
    return success_count == len(modules)

def test_optional_dependencies():
    """Test optional dependencies"""
    print("\n📦 Testing Optional Dependencies...")
    
    dependencies = [
        ("fastapi", "FastAPI"),
        ("pymongo", "MongoClient"),
        ("sklearn", "IsolationForest"),
        ("numpy", "array"),
        ("cryptography", "Cipher")
    ]
    
    available = []
    for dep_name, class_name in dependencies:
        try:
            if dep_name == "sklearn":
                from sklearn.ensemble import IsolationForest
                print(f"✅ {dep_name} - Available")
                available.append(dep_name)
            elif dep_name == "cryptography":
                from cryptography.hazmat.primitives.ciphers import Cipher
                print(f"✅ {dep_name} - Available")
                available.append(dep_name)
            else:
                module = __import__(dep_name)
                print(f"✅ {dep_name} - Available")
                available.append(dep_name)
        except ImportError:
            print(f"⚠️  {dep_name} - Not available (optional)")
    
    return available

def test_basic_functionality():
    """Test basic functionality"""
    print("\n🔧 Testing Basic Functionality...")
    
    try:
        from memory_protection import MemoryProtectionLayer
        
        config = {
            "memory_protection": {
                "enabled": True,
                "safe_mode": True,
                "simulation_only": True
            },
            "mongodb": {"enabled": False}
        }
        
        mp = MemoryProtectionLayer(config)
        print("✅ Memory Protection Layer initialization - OK")
        
        # Test encryption
        result = mp.encrypt_memory_partition()
        if result and result.get("success"):
            print("✅ Memory encryption - OK")
        else:
            print("❌ Memory encryption - FAILED")
            return False
        
        # Test wiping
        result = mp.wipe_memory_keys()
        if result and result.get("success"):
            print("✅ Memory wiping - OK")
        else:
            print("❌ Memory wiping - FAILED")
            return False
        
        # Test status
        status = mp.get_protection_status()
        if status and status.get("enabled"):
            print("✅ Protection status - OK")
        else:
            print("❌ Protection status - FAILED")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test - FAILED: {e}")
        traceback.print_exc()
        return False

def test_advanced_functionality():
    """Test advanced functionality"""
    print("\n🔧 Testing Advanced Functionality...")
    
    try:
        # Test advanced memory analysis
        from advanced_memory_analysis import AdvancedMemoryAnalyzer
        analyzer = AdvancedMemoryAnalyzer({})
        print("✅ Advanced Memory Analyzer - OK")
        
        # Test advanced encryption
        from advanced_memory_encryption import AdvancedMemoryEncryptor
        encryptor = AdvancedMemoryEncryptor({})
        print("✅ Advanced Memory Encryptor - OK")
        
        # Test cold boot protection
        from cold_boot_protection import ColdBootProtectionSystem
        protection = ColdBootProtectionSystem({})
        print("✅ Cold Boot Protection System - OK")
        
        # Test injection monitoring
        from injection_monitoring import MemoryInjectionMonitor
        monitor = MemoryInjectionMonitor({})
        print("✅ Memory Injection Monitor - OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced functionality test - FAILED: {e}")
        traceback.print_exc()
        return False

def test_api_server():
    """Test API server"""
    print("\n🌐 Testing API Server...")
    
    try:
        from python.optimized_api_server import app
        print("✅ Optimized API Server - OK")
        
        # Check if FastAPI is available
        try:
            import fastapi
            print("✅ FastAPI available - Full API functionality")
        except ImportError:
            print("⚠️  FastAPI not available - API server will use fallback mode")
        
        return True
        
    except Exception as e:
        print(f"❌ API server test - FAILED: {e}")
        return False

def test_configuration():
    """Test configuration files"""
    print("\n⚙️ Testing Configuration Files...")
    
    config_files = [
        "production_config.json",
        "windows_config.json"
    ]
    
    success = True
    for config_file in config_files:
        try:
            if os.path.exists(config_file):
                import json
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print(f"✅ {config_file} - Valid JSON")
            else:
                print(f"⚠️  {config_file} - Not found")
        except Exception as e:
            print(f"❌ {config_file} - FAILED: {e}")
            success = False
    
    return success

def test_file_structure():
    """Test file structure"""
    print("\n📁 Testing File Structure...")
    
    required_files = [
        "memory_protection.py",
        "advanced_memory_analysis.py",
        "advanced_memory_encryption.py",
        "cold_boot_protection.py",
        "injection_monitoring.py",
        "requirements.txt",
        "README.md"
    ]
    
    required_dirs = [
        "cpp",
        "python",
        "python/ml_models"
    ]
    
    success = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - Present")
        else:
            print(f"❌ {file_path} - Missing")
            success = False
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {dir_path}/ - Present")
        else:
            print(f"❌ {dir_path}/ - Missing")
            success = False
    
    return success

def main():
    """Run comprehensive verification"""
    print("Starting comprehensive verification...\n")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Core Imports", test_core_imports),
        ("Advanced Imports", test_advanced_imports),
        ("Optional Dependencies", lambda: test_optional_dependencies() is not None),
        ("Configuration Files", test_configuration),
        ("Basic Functionality", test_basic_functionality),
        ("Advanced Functionality", test_advanced_functionality),
        ("API Server", test_api_server)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - FAILED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 VERIFICATION SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print("-" * 80)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL VERIFICATION TESTS PASSED!")
        print("✅ Memory Protection Layer is ready for deployment")
        print("✅ All components are working correctly")
        print("✅ No errors or issues detected")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed - review required")
        print("❌ Some components may need attention before deployment")
    
    print("\n🛡️ SAFETY CONFIRMATION:")
    print("✅ All tests run in safe mode")
    print("✅ No system modifications were made")
    print("✅ Your system is completely safe")
    print("=" * 80)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
