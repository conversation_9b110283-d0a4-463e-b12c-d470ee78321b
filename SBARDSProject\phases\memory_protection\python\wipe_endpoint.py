"""
@file wipe_endpoint.py
@brief نقطة نهاية FastAPI لمسح مفاتيح الذاكرة في نظام SBARDS

هذا الملف يحتوي على واجهة برمجة التطبيقات (API) لمسح مفاتيح الذاكرة
التي قد تكون عرضة لهجمات Cold Boot
كما ورد في قسم 6.1.2 من وثيقة النظام
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import APIKeyHeader
from typing import Optional
import logging
try:
    from ..memory_protection import MemoryProtectionLayer
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from memory_protection import MemoryProtectionLayer

try:
    from ...config import load_config
except ImportError:
    # Fallback configuration loader
    def load_config():
        return {
            "memory_protection": {
                "enabled": True,
                "safe_mode": True,
                "simulation_only": True,
                "scan_interval_seconds": 120,
                "memory_threshold_mb": 200,
                "suspicious_patterns": []
            },
            "mongodb": {"enabled": False}
        }

# تهيئة المسار (Router) لنقاط النهاية
router = APIRouter()

# مصادقة مفتاح API
api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)

async def get_api_key(api_key: Optional[str] = Depends(api_key_header)):
    if api_key != "SBARDS_SECURE_API_KEY":  # في الواقع يجب أن يكون من إعدادات النظام
        raise HTTPException(
            status_code=403,
            detail="Invalid API Key"
        )
    return api_key

@router.post("/memory/wipe",
             summary="مسح مفاتيح الذاكرة",
             description="يقوم بمسح مفاتيح التشفير من الذاكرة بطريقة آمنة لمنع هجمات Cold Boot")
async def wipe_memory(api_key: str = Depends(get_api_key)):
    """
    نقطة نهاية لمسح مفاتيح الذاكرة

    Returns:
        dict: نتيجة عملية المسح
    """
    try:
        # تحميل الإعدادات وتهيئة طبقة حماية الذاكرة
        config = load_config()
        memory_protection = MemoryProtectionLayer(config)

        # استدعاء دالة المسح
        result = memory_protection.wipe_memory_keys()

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "Wipe failed")
            )

        return {
            "status": "success",
            "message": "Memory keys wiped successfully",
            "timestamp": result.get("timestamp")
        }

    except Exception as e:
        logging.error(f"Wipe endpoint error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Memory wipe failed"
        )