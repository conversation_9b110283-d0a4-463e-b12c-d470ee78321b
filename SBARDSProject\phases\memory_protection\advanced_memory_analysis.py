"""
Advanced Memory Analysis Module (4.3.4) - SBARDS Memory Protection Layer
Implements comprehensive memory image analysis, forensics techniques, and injection detection.
"""

import os
import sys
import time
import logging
import psutil
import mmap
import struct
import hashlib
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

# Optional dependencies for advanced analysis
try:
    import volatility3
    from volatility3.framework import contexts, automagic, plugins
    VOLATILITY_AVAILABLE = True
except ImportError:
    VOLATILITY_AVAILABLE = False

try:
    import yara
    YARA_AVAILABLE = True
except ImportError:
    YARA_AVAILABLE = False

@dataclass
class MemoryRegion:
    """Represents a memory region for analysis"""
    start_address: int
    end_address: int
    size: int
    permissions: str
    path: str
    is_executable: bool
    is_writable: bool
    is_suspicious: bool = False
    injection_score: float = 0.0

@dataclass
class MemoryImage:
    """Represents a captured memory image"""
    timestamp: datetime
    process_id: int
    process_name: str
    image_path: str
    size_bytes: int
    hash_md5: str
    hash_sha256: str
    regions: List[MemoryRegion]
    analysis_metadata: Dict[str, Any]

class AdvancedMemoryAnalyzer:
    """
    Advanced Memory Analysis Engine implementing section 4.3.4 requirements:
    - Memory Image Analysis
    - Memory Forensics Techniques
    - Code Injection Detection
    - Suspicious Area Analysis
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("SBARDS.MemoryAnalysis")

        # Analysis configuration
        self.analysis_config = config.get("memory_analysis", {})
        self.enable_volatility = self.analysis_config.get("enable_volatility", VOLATILITY_AVAILABLE)
        self.enable_yara = self.analysis_config.get("enable_yara", YARA_AVAILABLE)
        self.max_image_size = self.analysis_config.get("max_image_size_mb", 500) * 1024 * 1024

        # Memory image storage
        self.captured_images: List[MemoryImage] = []
        self.analysis_cache: Dict[str, Any] = {}

        # Injection detection patterns
        self.injection_patterns = self._load_injection_patterns()

        # Initialize YARA rules if available
        self.yara_rules = self._load_yara_rules() if YARA_AVAILABLE else None

        self.logger.info("Advanced Memory Analyzer initialized")

    def _load_injection_patterns(self) -> Dict[str, bytes]:
        """Load code injection detection patterns"""
        patterns = {
            # Common shellcode patterns
            'nop_sled': b'\x90' * 10,  # NOP sled
            'call_pop': b'\xe8\x00\x00\x00\x00\x58',  # call $+5; pop eax
            'jmp_call_pop': b'\xeb\x03\xe8\xf8\xff\xff\xff',  # jmp/call/pop technique

            # ROP gadgets
            'pop_ret': b'\x58\xc3',  # pop eax; ret
            'pop_pop_ret': b'\x58\x59\xc3',  # pop eax; pop ecx; ret

            # Process hollowing indicators
            'nt_unmap_view': b'NtUnmapViewOfSection',
            'nt_write_virtual': b'NtWriteVirtualMemory',

            # DLL injection patterns
            'load_library': b'LoadLibraryA',
            'get_proc_addr': b'GetProcAddress',
            'virtual_alloc': b'VirtualAlloc',
        }
        return patterns

    def _load_yara_rules(self) -> Optional[Any]:
        """Load YARA rules for memory analysis"""
        if not YARA_AVAILABLE:
            return None

        try:
            # Basic YARA rules for memory analysis
            rules_source = '''
            rule Shellcode_Detection {
                meta:
                    description = "Detects potential shellcode patterns"
                strings:
                    $nop_sled = { 90 90 90 90 90 90 90 90 90 90 }
                    $call_pop = { E8 00 00 00 00 58 }
                    $jmp_call_pop = { EB 03 E8 F8 FF FF FF }
                condition:
                    any of them
            }

            rule Code_Injection {
                meta:
                    description = "Detects code injection indicators"
                strings:
                    $api1 = "VirtualAlloc" ascii
                    $api2 = "WriteProcessMemory" ascii
                    $api3 = "CreateRemoteThread" ascii
                condition:
                    2 of them
            }

            rule Process_Hollowing {
                meta:
                    description = "Detects process hollowing techniques"
                strings:
                    $api1 = "NtUnmapViewOfSection" ascii
                    $api2 = "NtWriteVirtualMemory" ascii
                    $api3 = "ResumeThread" ascii
                condition:
                    all of them
            }
            '''

            return yara.compile(source=rules_source)
        except Exception as e:
            self.logger.warning(f"Failed to compile YARA rules: {e}")
            return None

    def capture_memory_image(self, process_id: int, output_dir: str = "memory_images") -> Optional[MemoryImage]:
        """
        Capture memory image at specific point in time (*******)
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            process = psutil.Process(process_id)

            timestamp = datetime.now()
            image_filename = f"memory_image_{process.name()}_{process_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}.img"
            image_path = os.path.join(output_dir, image_filename)

            # Analyze memory regions first
            regions = self._analyze_memory_regions(process_id)

            # Create memory image (simulation for safety)
            image_data = self._create_memory_image(process, regions)

            # Calculate hashes
            hash_md5 = hashlib.md5(image_data).hexdigest()
            hash_sha256 = hashlib.sha256(image_data).hexdigest()

            # Save image to disk (in production, this would be actual memory content)
            with open(image_path, 'wb') as f:
                f.write(image_data)

            # Create memory image object
            memory_image = MemoryImage(
                timestamp=timestamp,
                process_id=process_id,
                process_name=process.name(),
                image_path=image_path,
                size_bytes=len(image_data),
                hash_md5=hash_md5,
                hash_sha256=hash_sha256,
                regions=regions,
                analysis_metadata={
                    "capture_method": "safe_simulation",
                    "platform": sys.platform,
                    "analyzer_version": "2.0.0"
                }
            )

            self.captured_images.append(memory_image)
            self.logger.info(f"Memory image captured: {image_path} ({len(image_data)} bytes)")

            return memory_image

        except Exception as e:
            self.logger.error(f"Failed to capture memory image for PID {process_id}: {e}")
            return None

    def _create_memory_image(self, process: psutil.Process, regions: List[MemoryRegion]) -> bytes:
        """Create simulated memory image for analysis"""
        # In production, this would capture actual memory content
        # For safety, we create a representative image with metadata

        image_data = b""

        # Add process metadata
        metadata = {
            "pid": process.pid,
            "name": process.name(),
            "exe": process.exe() if process.exe() else "unknown",
            "cmdline": process.cmdline(),
            "create_time": process.create_time(),
            "memory_info": process.memory_info()._asdict(),
            "num_regions": len(regions)
        }

        # Serialize metadata
        import json
        metadata_json = json.dumps(metadata, indent=2).encode('utf-8')
        image_data += b"SBARDS_MEMORY_IMAGE_HEADER\n"
        image_data += metadata_json
        image_data += b"\nSBARDS_MEMORY_REGIONS\n"

        # Add region information
        for region in regions:
            region_info = f"Region: {region.start_address:016x}-{region.end_address:016x} "
            region_info += f"Size: {region.size} Perms: {region.permissions} Path: {region.path}\n"
            image_data += region_info.encode('utf-8')

        # Add simulated memory content patterns
        image_data += b"\nSBARDS_MEMORY_CONTENT\n"
        image_data += b"[Simulated memory content for analysis - Safe mode]\n"

        # Add some realistic patterns for analysis testing
        image_data += b"\x90" * 100  # NOP sled simulation
        image_data += b"LoadLibraryA\x00GetProcAddress\x00"  # API names
        image_data += b"\x48\x89\xe5"  # x64 function prologue

        return image_data

    def _analyze_memory_regions(self, process_id: int) -> List[MemoryRegion]:
        """Analyze memory regions of a process"""
        regions = []

        try:
            process = psutil.Process(process_id)

            # Get memory maps (cross-platform)
            try:
                memory_maps = process.memory_maps()
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                self.logger.warning(f"Cannot access memory maps for PID {process_id}")
                return regions

            for mmap_info in memory_maps:
                # Parse memory region information
                addr_parts = mmap_info.addr.split('-')
                if len(addr_parts) == 2:
                    start_addr = int(addr_parts[0], 16)
                    end_addr = int(addr_parts[1], 16)
                    size = end_addr - start_addr

                    # Determine permissions and characteristics
                    perms = getattr(mmap_info, 'perms', 'unknown')
                    path = getattr(mmap_info, 'path', 'anonymous')

                    is_executable = 'x' in perms
                    is_writable = 'w' in perms

                    # Detect suspicious characteristics
                    is_suspicious = self._is_region_suspicious(start_addr, end_addr, perms, path)
                    injection_score = self._calculate_injection_score(start_addr, end_addr, perms, path)

                    region = MemoryRegion(
                        start_address=start_addr,
                        end_address=end_addr,
                        size=size,
                        permissions=perms,
                        path=path,
                        is_executable=is_executable,
                        is_writable=is_writable,
                        is_suspicious=is_suspicious,
                        injection_score=injection_score
                    )

                    regions.append(region)

        except Exception as e:
            self.logger.error(f"Error analyzing memory regions for PID {process_id}: {e}")

        return regions

    def _is_region_suspicious(self, start_addr: int, end_addr: int, perms: str, path: str) -> bool:
        """Determine if a memory region is suspicious"""
        # Writable and executable regions are suspicious
        if 'w' in perms and 'x' in perms:
            return True

        # Anonymous executable regions
        if 'x' in perms and (path == 'anonymous' or '[heap]' in path):
            return True

        # Unusual memory locations
        if start_addr < 0x10000:  # Very low addresses
            return True

        return False

    def _calculate_injection_score(self, start_addr: int, end_addr: int, perms: str, path: str) -> float:
        """Calculate injection likelihood score for memory region"""
        score = 0.0

        # Executable + writable = high risk
        if 'w' in perms and 'x' in perms:
            score += 0.4

        # Anonymous executable regions
        if 'x' in perms and path == 'anonymous':
            score += 0.3

        # Heap executable regions
        if 'x' in perms and '[heap]' in path:
            score += 0.5

        # Unusual size patterns
        size = end_addr - start_addr
        if size < 4096:  # Very small executable regions
            score += 0.2

        return min(1.0, score)

    def analyze_data_structures(self, memory_image: MemoryImage) -> Dict[str, Any]:
        """
        Analysis of data structures in memory (*******)
        """
        analysis_results = {
            "timestamp": datetime.now().isoformat(),
            "image_hash": memory_image.hash_sha256,
            "structures_found": [],
            "suspicious_structures": [],
            "analysis_summary": {}
        }

        try:
            # Read memory image for analysis
            with open(memory_image.image_path, 'rb') as f:
                image_data = f.read()

            # Analyze PE headers if present
            pe_structures = self._analyze_pe_structures(image_data)
            analysis_results["structures_found"].extend(pe_structures)

            # Analyze stack frames
            stack_analysis = self._analyze_stack_structures(image_data, memory_image.regions)
            analysis_results["structures_found"].extend(stack_analysis)

            # Analyze heap structures
            heap_analysis = self._analyze_heap_structures(image_data, memory_image.regions)
            analysis_results["structures_found"].extend(heap_analysis)

            # Identify suspicious structures
            analysis_results["suspicious_structures"] = [
                struct for struct in analysis_results["structures_found"]
                if struct.get("suspicious", False)
            ]

            # Generate summary
            analysis_results["analysis_summary"] = {
                "total_structures": len(analysis_results["structures_found"]),
                "suspicious_count": len(analysis_results["suspicious_structures"]),
                "risk_level": self._calculate_structure_risk_level(analysis_results)
            }

            self.logger.info(f"Data structure analysis completed: {analysis_results['analysis_summary']}")

        except Exception as e:
            self.logger.error(f"Data structure analysis failed: {e}")
            analysis_results["error"] = str(e)

        return analysis_results

    def detect_injected_code(self, memory_image: MemoryImage) -> Dict[str, Any]:
        """
        Detecting injected code (*******)
        """
        detection_results = {
            "timestamp": datetime.now().isoformat(),
            "image_hash": memory_image.hash_sha256,
            "injections_detected": [],
            "injection_techniques": [],
            "risk_score": 0.0
        }

        try:
            # Read memory image
            with open(memory_image.image_path, 'rb') as f:
                image_data = f.read()

            # Pattern-based injection detection
            pattern_detections = self._detect_injection_patterns(image_data)
            detection_results["injections_detected"].extend(pattern_detections)

            # YARA-based detection if available
            if self.yara_rules:
                yara_detections = self._yara_scan_memory(image_data)
                detection_results["injections_detected"].extend(yara_detections)

            # Analyze suspicious memory regions
            region_analysis = self._analyze_suspicious_regions(memory_image.regions)
            detection_results["injections_detected"].extend(region_analysis)

            # Detect specific injection techniques
            detection_results["injection_techniques"] = self._identify_injection_techniques(image_data)

            # Calculate overall risk score
            detection_results["risk_score"] = self._calculate_injection_risk_score(detection_results)

            self.logger.info(f"Code injection detection completed: {len(detection_results['injections_detected'])} detections")

        except Exception as e:
            self.logger.error(f"Code injection detection failed: {e}")
            detection_results["error"] = str(e)

        return detection_results

    def perform_memory_forensics(self, memory_image: MemoryImage) -> Dict[str, Any]:
        """
        Memory Forensics Techniques (4.3.4.2)
        Using Volatility framework tools for analysis
        """
        forensics_results = {
            "timestamp": datetime.now().isoformat(),
            "image_hash": memory_image.hash_sha256,
            "volatility_analysis": {},
            "extracted_artifacts": [],
            "stealth_techniques": [],
            "stack_heap_analysis": {}
        }

        try:
            # Volatility analysis if available
            if self.enable_volatility and VOLATILITY_AVAILABLE:
                forensics_results["volatility_analysis"] = self._run_volatility_analysis(memory_image)

            # Extract keys and certificates
            forensics_results["extracted_artifacts"] = self._extract_crypto_artifacts(memory_image)

            # Detect stealth techniques
            forensics_results["stealth_techniques"] = self._detect_stealth_techniques(memory_image)

            # Stack/Heap analysis for suspicious processes
            forensics_results["stack_heap_analysis"] = self._analyze_stack_heap(memory_image)

            self.logger.info("Memory forensics analysis completed")

        except Exception as e:
            self.logger.error(f"Memory forensics analysis failed: {e}")
            forensics_results["error"] = str(e)

        return forensics_results

    def _analyze_pe_structures(self, image_data: bytes) -> List[Dict[str, Any]]:
        """Analyze PE (Portable Executable) structures in memory"""
        structures = []

        # Look for PE signatures
        pe_signature = b'MZ'
        offset = 0

        while True:
            offset = image_data.find(pe_signature, offset)
            if offset == -1:
                break

            try:
                # Basic PE header analysis
                if offset + 64 < len(image_data):
                    # Check for PE signature at e_lfanew offset
                    e_lfanew_offset = offset + 60
                    if e_lfanew_offset + 4 <= len(image_data):
                        e_lfanew = struct.unpack('<I', image_data[e_lfanew_offset:e_lfanew_offset+4])[0]

                        if offset + e_lfanew + 4 <= len(image_data):
                            pe_sig = image_data[offset + e_lfanew:offset + e_lfanew + 4]
                            if pe_sig == b'PE\x00\x00':
                                structures.append({
                                    "type": "PE_Header",
                                    "offset": offset,
                                    "size": 64,
                                    "suspicious": offset < 0x1000,  # Suspicious if at very low address
                                    "details": {
                                        "pe_offset": e_lfanew,
                                        "location": "memory"
                                    }
                                })
            except:
                pass

            offset += 1

        return structures

    def _analyze_stack_structures(self, image_data: bytes, regions: List[MemoryRegion]) -> List[Dict[str, Any]]:
        """Analyze stack structures for anomalies"""
        structures = []

        # Find stack regions
        stack_regions = [r for r in regions if '[stack]' in r.path or 'stack' in r.path.lower()]

        for region in stack_regions:
            # Look for return address patterns
            # This is a simplified analysis - in production would be more sophisticated
            structures.append({
                "type": "Stack_Region",
                "start_address": region.start_address,
                "size": region.size,
                "suspicious": region.is_suspicious or region.injection_score > 0.5,
                "details": {
                    "permissions": region.permissions,
                    "injection_score": region.injection_score
                }
            })

        return structures

    def _analyze_heap_structures(self, image_data: bytes, regions: List[MemoryRegion]) -> List[Dict[str, Any]]:
        """Analyze heap structures for anomalies"""
        structures = []

        # Find heap regions
        heap_regions = [r for r in regions if '[heap]' in r.path or 'heap' in r.path.lower()]

        for region in heap_regions:
            structures.append({
                "type": "Heap_Region",
                "start_address": region.start_address,
                "size": region.size,
                "suspicious": region.is_executable,  # Executable heap is suspicious
                "details": {
                    "permissions": region.permissions,
                    "executable": region.is_executable,
                    "injection_score": region.injection_score
                }
            })

        return structures

    def _detect_injection_patterns(self, image_data: bytes) -> List[Dict[str, Any]]:
        """Detect code injection patterns in memory"""
        detections = []

        for pattern_name, pattern_bytes in self.injection_patterns.items():
            offset = 0
            while True:
                offset = image_data.find(pattern_bytes, offset)
                if offset == -1:
                    break

                detections.append({
                    "type": "Pattern_Detection",
                    "pattern": pattern_name,
                    "offset": offset,
                    "size": len(pattern_bytes),
                    "risk_level": self._get_pattern_risk_level(pattern_name),
                    "description": self._get_pattern_description(pattern_name)
                })

                offset += 1

        return detections

    def _yara_scan_memory(self, image_data: bytes) -> List[Dict[str, Any]]:
        """Scan memory using YARA rules"""
        detections = []

        try:
            matches = self.yara_rules.match(data=image_data)
            for match in matches:
                detections.append({
                    "type": "YARA_Detection",
                    "rule": match.rule,
                    "meta": dict(match.meta),
                    "strings": [{"offset": s.offset, "identifier": s.identifier, "length": s.length}
                              for s in match.strings],
                    "risk_level": "high"
                })
        except Exception as e:
            self.logger.warning(f"YARA scan failed: {e}")

        return detections

    def _analyze_suspicious_regions(self, regions: List[MemoryRegion]) -> List[Dict[str, Any]]:
        """Analyze suspicious memory regions"""
        detections = []

        for region in regions:
            if region.is_suspicious or region.injection_score > 0.5:
                detections.append({
                    "type": "Suspicious_Region",
                    "start_address": region.start_address,
                    "end_address": region.end_address,
                    "size": region.size,
                    "permissions": region.permissions,
                    "path": region.path,
                    "injection_score": region.injection_score,
                    "risk_level": "high" if region.injection_score > 0.7 else "medium"
                })

        return detections

    def _identify_injection_techniques(self, image_data: bytes) -> List[str]:
        """Identify specific injection techniques"""
        techniques = []

        # Check for DLL injection indicators
        if b'LoadLibraryA' in image_data and b'GetProcAddress' in image_data:
            techniques.append("DLL_Injection")

        # Check for process hollowing
        if b'NtUnmapViewOfSection' in image_data and b'NtWriteVirtualMemory' in image_data:
            techniques.append("Process_Hollowing")

        # Check for reflective DLL loading
        if b'VirtualAlloc' in image_data and b'VirtualProtect' in image_data:
            techniques.append("Reflective_DLL_Loading")

        # Check for thread hijacking
        if b'SuspendThread' in image_data and b'SetThreadContext' in image_data:
            techniques.append("Thread_Hijacking")

        return techniques

    def _calculate_injection_risk_score(self, detection_results: Dict[str, Any]) -> float:
        """Calculate overall injection risk score"""
        score = 0.0

        # Weight different detection types
        for detection in detection_results["injections_detected"]:
            if detection["type"] == "YARA_Detection":
                score += 0.3
            elif detection["type"] == "Pattern_Detection":
                score += 0.2
            elif detection["type"] == "Suspicious_Region":
                score += detection.get("injection_score", 0.1)

        # Bonus for multiple injection techniques
        technique_count = len(detection_results["injection_techniques"])
        if technique_count > 1:
            score += technique_count * 0.1

        return min(1.0, score)

    def _get_pattern_risk_level(self, pattern_name: str) -> str:
        """Get risk level for injection pattern"""
        high_risk_patterns = ['nop_sled', 'jmp_call_pop', 'nt_unmap_view']
        medium_risk_patterns = ['pop_ret', 'load_library', 'virtual_alloc']

        if pattern_name in high_risk_patterns:
            return "high"
        elif pattern_name in medium_risk_patterns:
            return "medium"
        else:
            return "low"

    def _get_pattern_description(self, pattern_name: str) -> str:
        """Get description for injection pattern"""
        descriptions = {
            'nop_sled': "NOP sled commonly used in shellcode",
            'call_pop': "Call/pop technique for position-independent code",
            'jmp_call_pop': "Jump/call/pop shellcode technique",
            'pop_ret': "ROP gadget for return-oriented programming",
            'pop_pop_ret': "Multi-pop ROP gadget",
            'nt_unmap_view': "API used in process hollowing",
            'nt_write_virtual': "API used for memory injection",
            'load_library': "DLL loading API",
            'get_proc_addr': "API resolution function",
            'virtual_alloc': "Memory allocation API"
        }
        return descriptions.get(pattern_name, "Unknown injection pattern")

    def _calculate_structure_risk_level(self, analysis_results: Dict[str, Any]) -> str:
        """Calculate risk level based on structure analysis"""
        suspicious_count = analysis_results["analysis_summary"]["suspicious_count"]
        total_count = analysis_results["analysis_summary"]["total_structures"]

        if total_count == 0:
            return "low"

        suspicious_ratio = suspicious_count / total_count

        if suspicious_ratio > 0.5:
            return "high"
        elif suspicious_ratio > 0.2:
            return "medium"
        else:
            return "low"