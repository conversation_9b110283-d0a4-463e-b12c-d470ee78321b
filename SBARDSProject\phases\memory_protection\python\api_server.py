"""
Memory Protection API Server - Enhanced Version
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from slowapi import Limiter
from slowapi.util import get_remote_address
from typing import Optional
import logging
import os
from datetime import datetime
try:
    from .encrypt_endpoint import router as encrypt_router
    from .wipe_endpoint import router as wipe_router
except ImportError:
    # Create fallback routers if imports fail
    from fastapi import APIRouter
    encrypt_router = APIRouter()
    wipe_router = APIRouter()

try:
    from ..memory_protection import MemoryProtectionLayer
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from memory_protection import MemoryProtectionLayer

try:
    from ...config import load_config
except ImportError:
    # Fallback configuration loader
    def load_config():
        return {
            "memory_protection": {
                "enabled": True,
                "safe_mode": True,
                "simulation_only": True,
                "scan_interval_seconds": 120,
                "memory_threshold_mb": 200,
                "suspicious_patterns": []
            },
            "mongodb": {"enabled": False}
        }

# Initialize FastAPI app with security settings
app = FastAPI(
    title="SBARDS Memory Protection API",
    description="Secure API endpoints for memory protection operations",
    version="2.0.0",
    docs_url=None if os.getenv("ENVIRONMENT") == "production" else "/docs",
    redoc_url=None
)

# Security middleware
app.add_middleware(HTTPSRedirectMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "https://sbards.example.com").split(","),
    allow_credentials=True,
    allow_methods=["POST", "GET"],
    allow_headers=["X-API-KEY"],
)

# Rate limiting
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter

# API key authentication
api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)

async def validate_api_key(api_key: Optional[str] = Depends(api_key_header)):
    """Validate API key from environment variables"""
    if api_key != os.getenv("SBARDS_API_KEY"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid API Key"
        )
    return api_key

# Include endpoints from other files
app.include_router(
    encrypt_router,
    prefix="/api/v1",
    dependencies=[Depends(validate_api_key)],
    tags=["Memory Encryption"]
)

app.include_router(
    wipe_router,
    prefix="/api/v1",
    dependencies=[Depends(validate_api_key)],
    tags=["Memory Wiping"]
)

@app.on_event("startup")
async def startup_event():
    """Initialize memory protection layer on startup"""
    try:
        config = load_config()
        app.state.memory_protection = MemoryProtectionLayer(config)
        logging.info("Memory Protection API initialized successfully")
    except Exception as e:
        logging.error(f"Failed to initialize Memory Protection API: {e}")
        raise

@app.post("/api/v1/memory/encrypt")
@limiter.limit("5/minute")
async def encrypt_memory(
    request: Request,
    api_key: str = Depends(validate_api_key)
):
    """Endpoint to encrypt memory partition with enhanced error handling"""
    try:
        if not hasattr(app.state, 'memory_protection'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Memory protection service not initialized"
            )

        result = app.state.memory_protection.encrypt_memory_partition()

        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("message", "Memory encryption failed")
            )

        return {
            "status": "success",
            "message": result.get("message"),
            "timestamp": result.get("timestamp")
        }
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Encryption endpoint error: {str(e)[:200]}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during memory encryption"
        )

@app.post("/api/v1/memory/wipe")
@limiter.limit("5/minute")
async def wipe_memory(
    request: Request,
    api_key: str = Depends(validate_api_key)
):
    """Endpoint to wipe memory keys with enhanced error handling"""
    try:
        if not hasattr(app.state, 'memory_protection'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Memory protection service not initialized"
            )

        result = app.state.memory_protection.wipe_memory_keys()

        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("message", "Memory wipe failed")
            )

        return {
            "status": "success",
            "message": result.get("message"),
            "timestamp": result.get("timestamp")
        }
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Wipe endpoint error: {str(e)[:200]}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during memory wipe"
        )

@app.get("/api/v1/memory/status")
@limiter.limit("10/minute")
async def get_status(
    request: Request,
    api_key: str = Depends(validate_api_key)
):
    """Endpoint to get protection status with enhanced error handling"""
    try:
        if not hasattr(app.state, 'memory_protection'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Memory protection service not initialized"
            )

        status_result = app.state.memory_protection.get_protection_status()

        if status_result.get("error"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=status_result.get("error")
            )

        return status_result
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Status endpoint error: {str(e)[:200]}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while getting protection status"
        )

@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }