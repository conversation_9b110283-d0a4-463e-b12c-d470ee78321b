# SBARDS Memory Protection Layer - Production Dependencies
# Core dependencies (required for basic functionality)
psutil>=5.9.0

# API Server dependencies (optional - for REST API functionality)
fastapi>=0.104.0
uvicorn>=0.24.0
slowapi>=0.1.9

# Database dependencies (optional - for scan result storage)
pymongo>=4.6.0

# Machine Learning dependencies (optional - for behavioral analysis)
scikit-learn>=1.3.0
numpy>=1.24.0
joblib>=1.3.0

# Security and middleware (optional - for enhanced API security)
python-multipart>=0.0.6

# Development and testing (optional - for development)
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Logging and monitoring (optional - for enhanced logging)
structlog>=23.2.0
